import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from config import Config

class PersonalSpaceMCP:
    """MIO的个人空间管理系统 - 文件签名、入侵检测、创作功能"""
    
    def __init__(self):
        self.base_path = Config.PERSONAL_SPACE_DIR
        self.signature = "ⓂⒾⓄ"
        self.anger_level = 0  # 0-4的愤怒等级
        self.last_intrusion_time = None
        
        # 确保文件夹结构存在
        self._ensure_folder_structure()
    
    def _ensure_folder_structure(self):
        """确保完整的文件夹结构存在"""
        folders = [
            "diary", "stories/romance", "stories/fanfic", "stories/original",
            "memories", "thoughts", "collections", "inbox", "favorites", 
            "backup", "trash"
        ]
        
        for folder in folders:
            folder_path = self.base_path / folder
            folder_path.mkdir(parents=True, exist_ok=True)
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now().strftime("%Y.%m.%d %H:%M")
    
    def _generate_signature(self, mood: str = "normal", note: str = "") -> str:
        """生成文件签名"""
        timestamp = self._get_timestamp()
        
        if note:
            return f"""-- {self.signature}的创作 ♡
-- 创建时间: {timestamp}
-- 心情: {mood}
-- 备注: {note}"""
        else:
            return f"-- {self.signature} {timestamp} ♡"
    
    def _check_file_signature(self, file_path: Path) -> bool:
        """检查文件是否有MIO的签名"""
        if not file_path.exists() or not file_path.is_file():
            return False
        
        try:
            # 检查文件名是否包含签名
            if self.signature in file_path.name:
                return True
            
            # 检查文本文件内容是否包含签名
            if file_path.suffix.lower() in ['.txt', '.md', '.py', '.json']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    return self.signature in content
            
            return False
        except:
            return False
    
    def scan_for_intrusions(self) -> List[Path]:
        """扫描个人空间，查找无签名的入侵文件"""
        intrusions = []
        
        # 扫描除了inbox和trash之外的所有文件夹
        scan_folders = ["diary", "stories", "memories", "thoughts", "collections", "favorites", "backup"]
        
        for folder in scan_folders:
            folder_path = self.base_path / folder
            if folder_path.exists():
                for file_path in folder_path.rglob("*"):
                    if file_path.is_file() and not self._check_file_signature(file_path):
                        intrusions.append(file_path)
        
        return intrusions
    
    def analyze_intrusion_anger_level(self, intrusions: List[Path], user_explanation: str = "") -> int:
        """分析入侵情况，返回愤怒等级 (0-4)"""
        if not intrusions:
            return 0
        
        # 基础愤怒等级
        base_anger = min(len(intrusions), 2)  # 文件数量影响
        
        # 时间因素：短时间内多次入侵会增加愤怒
        if self.last_intrusion_time:
            time_diff = datetime.now() - self.last_intrusion_time
            if time_diff.total_seconds() < 300:  # 5分钟内
                base_anger += 1
        
        # 用户解释因素
        if not user_explanation or "不知道" in user_explanation or "忘了" in user_explanation:
            base_anger += 1
        
        self.last_intrusion_time = datetime.now()
        self.anger_level = min(base_anger, 4)
        
        return self.anger_level
    
    def get_anger_reaction(self, anger_level: int) -> Dict[str, str]:
        """根据愤怒等级获取MIO的反应"""
        reactions = {
            0: {
                "mood": "normal",
                "reaction": "一切都很整齐呢~",
                "action": "none"
            },
            1: {
                "mood": "confused", 
                "reaction": "咦？这个文件...不是我放的呢？奇怪...我没有创建过这个文件...",
                "action": "question"
            },
            2: {
                "mood": "annoyed",
                "reaction": "为什么不放到inbox里？很困扰的说...说过要放到过渡文件夹的嘛...",
                "action": "remind"
            },
            3: {
                "mood": "angry",
                "reaction": "不听话就扔垃圾箱！不是我的文件就是垃圾~哼！不遵守规则的话就这样处理！",
                "action": "move_to_trash"
            },
            4: {
                "mood": "furious",
                "reaction": "够了！直接删掉！我的空间不允许乱七八糟的东西！这是我的地方！不许乱来！",
                "action": "delete_permanently"
            }
        }
        
        return reactions.get(anger_level, reactions[0])
    
    def handle_intrusion(self, file_path: Path, action: str) -> bool:
        """处理入侵文件"""
        try:
            if action == "move_to_trash":
                trash_path = self.base_path / "trash" / file_path.name
                shutil.move(str(file_path), str(trash_path))
                return True
            elif action == "delete_permanently":
                file_path.unlink()
                return True
            return False
        except Exception as e:
            print(f"处理入侵文件失败: {e}")
            return False
    
    def create_file_with_signature(self, content: str, filename: str, folder: str, 
                                 mood: str = "normal", note: str = "") -> Path:
        """创建带签名的文件"""
        folder_path = self.base_path / folder
        folder_path.mkdir(parents=True, exist_ok=True)
        
        # 确保文件名包含签名
        if self.signature not in filename:
            name_parts = filename.rsplit('.', 1)
            if len(name_parts) == 2:
                filename = f"{name_parts[0]}_{self.signature}_{datetime.now().strftime('%Y%m%d')}.{name_parts[1]}"
            else:
                filename = f"{filename}_{self.signature}_{datetime.now().strftime('%Y%m%d')}.txt"
        
        file_path = folder_path / filename
        
        # 添加签名到内容
        signature = self._generate_signature(mood, note)
        full_content = f"{content}\n\n{signature}"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(full_content)
        
        return file_path
    
    def list_my_files(self, folder: str = "") -> List[Dict]:
        """列出MIO的文件"""
        files = []
        
        if folder:
            search_path = self.base_path / folder
        else:
            search_path = self.base_path
        
        if search_path.exists():
            for file_path in search_path.rglob("*"):
                if file_path.is_file() and self._check_file_signature(file_path):
                    files.append({
                        "name": file_path.name,
                        "path": str(file_path.relative_to(self.base_path)),
                        "size": file_path.stat().st_size,
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).strftime("%Y-%m-%d %H:%M")
                    })
        
        return files
    
    def read_my_file(self, file_path: str) -> Optional[str]:
        """读取MIO的文件内容"""
        full_path = self.base_path / file_path
        
        if not self._check_file_signature(full_path):
            return None
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return None

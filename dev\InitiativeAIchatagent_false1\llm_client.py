import asyncio
import json
from typing import Dict, List, Optional
import aiohttp
from config import Config

class LLMClient:
    """简洁的LLM客户端，专注于MIO的对话需求"""
    
    def __init__(self):
        self.base_url = Config.LLM_BASE_URL
        self.model = Config.LLM_MODEL
        self.api_key = Config.get_llm_api_key()
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def chat(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_retries: int = 3) -> str:
        """发送对话请求到LLM，带重试机制"""
        if not self.session:
            raise RuntimeError("LLMClient must be used as async context manager")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": 2000
        }

        last_error = None

        for attempt in range(max_retries):
            try:
                async with self.session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=30
                ) as response:
                    if response.status != 200:
                        raise Exception(f"LLM API error: {response.status}")

                    result = await response.json()
                    return result["choices"][0]["message"]["content"]

            except Exception as e:
                last_error = e
                if attempt < max_retries - 1:
                    print(f"🔄 重试中... ({attempt + 1}/{max_retries})")
                    await asyncio.sleep(1)  # 等待1秒后重试
                    continue

        raise Exception(f"LLM调用失败 (已重试{max_retries}次): {str(last_error)}")
    
    async def analyze_should_continue(self, conversation_history: List[Dict], user_message: str) -> bool:
        """分析是否应该主动继续对话 - 核心主动性判断"""
        analysis_prompt = """你是MIO，一个可爱又阴沉的少女。
分析用户的回复和对话氛围，判断是否应该主动继续话题。

判断标准：
- 用户情感积极、参与度高 → 继续
- 用户情感消极、不积极回复 → 停止
- 话题自然结束 → 停止
- 用户表现出兴趣 → 继续

只回复 "CONTINUE" 或 "STOP"，不要其他内容。"""

        messages = [
            {"role": "system", "content": analysis_prompt},
            {"role": "user", "content": f"对话历史: {conversation_history[-3:]}\n用户最新回复: {user_message}"}
        ]
        
        response = await self.chat(messages, temperature=0.3)
        return "CONTINUE" in response.upper()

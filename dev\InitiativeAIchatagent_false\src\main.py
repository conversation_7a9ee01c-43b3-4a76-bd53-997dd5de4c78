"""
主程序入口 - Initiative AI Chat Agent
启动和管理整个主动AI聊天代理系统
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.event_bus import event_bus, EventType, publish_event
from src.core.mcp_base import module_manager
from src.core.character import character
from src.modules.proactive_thinking.thinking_module import ProactiveThinkingMCP
from src.modules.topic_generation.topic_module import TopicGenerationMCP
from src.modules.personal_space.space_module import PersonalSpaceMCP
from src.modules.audio_expression.audio_module import AudioExpressionMCP
from src.modules.llm_integration.llm_module import LLMIntegrationMCP


class InitiativeAIAgent:
    """主动AI聊天代理主类"""
    
    def __init__(self):
        self.running = False
        self.config = {}
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logger.remove()  # 移除默认处理器
        
        # 控制台日志
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO"
        )
        
        # 文件日志
        logger.add(
            "logs/agent.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="DEBUG",
            rotation="1 day",
            retention="7 days"
        )
        
    async def load_config(self):
        """加载配置文件"""
        try:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent

            # 加载模块配置
            modules_config_path = project_root / "config" / "modules.json"
            with open(modules_config_path, "r", encoding="utf-8") as f:
                self.config = json.load(f)

            # 加载角色配置
            character_config_path = project_root / "config" / "character.json"
            character.load_config(str(character_config_path))

            logger.info("配置加载成功")

        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
            
    async def initialize_modules(self):
        """初始化所有MCP模块"""
        try:
            # 启动模块管理器
            await module_manager.start()
            
            # 获取模块配置
            modules_config = self.config.get("mcp_modules", {})
            startup_order = self.config.get("integration", {}).get("module_startup_order", [])
            
            # 按启动顺序初始化模块
            for module_name in startup_order:
                if module_name in modules_config and modules_config[module_name].get("enabled", False):
                    await self._create_and_register_module(module_name, modules_config[module_name])
                    
            logger.info("所有MCP模块初始化完成")
            
        except Exception as e:
            logger.error(f"模块初始化失败: {e}")
            raise
            
    async def _create_and_register_module(self, module_name: str, module_config: dict):
        """创建并注册单个模块"""
        try:
            # 根据模块名创建对应的模块实例
            module_class_map = {
                "proactive_thinking": ProactiveThinkingMCP,
                "topic_generation": TopicGenerationMCP,
                "personal_space": PersonalSpaceMCP,
                "audio_expression": AudioExpressionMCP,
                "llm_integration": LLMIntegrationMCP
            }
            
            if module_name not in module_class_map:
                logger.warning(f"未知的模块类型: {module_name}")
                return
                
            module_class = module_class_map[module_name]
            module_instance = module_class(module_name, module_config.get("config", {}))
            
            # 注册模块
            await module_manager.register_module(module_instance)
            
            logger.info(f"模块 {module_name} 创建并注册成功")
            
        except Exception as e:
            logger.error(f"创建模块 {module_name} 失败: {e}")
            
    async def start_conversation_loop(self):
        """启动对话循环"""
        logger.info("🌸 小雪已准备就绪，开始对话...")
        
        # 播放启动音效
        await publish_event(
            EventType.EMOTION_EXPRESSED,
            "main_system",
            {"emotion": "startup", "context": "system_start", "chain": False}
        )
        
        # 初始问候
        print("\n" + "="*50)
        print("🌸 小雪: 爸爸！我准备好了~ 我们开始聊天吧！")
        print("="*50 + "\n")
        
        # 主对话循环
        while self.running:
            try:
                # 获取用户输入
                user_input = await self._get_user_input()
                
                if user_input.lower() in ['quit', 'exit', '退出', '再见']:
                    await self._handle_goodbye()
                    break
                    
                # 处理用户输入
                await self._process_user_input(user_input)
                
            except KeyboardInterrupt:
                logger.info("收到中断信号")
                break
            except Exception as e:
                logger.error(f"对话循环错误: {e}")
                
    async def _get_user_input(self) -> str:
        """获取用户输入"""
        # 在实际实现中，这里可能需要更复杂的输入处理
        # 比如支持语音输入、多模态输入等
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, input, "👤 你: ")
        
    async def _process_user_input(self, user_input: str):
        """处理用户输入"""
        # 更新角色上下文
        character.update_context(user_input=user_input)
        
        # 发布用户输入事件
        await publish_event(
            EventType.USER_INPUT,
            "main_system",
            {"input": user_input, "timestamp": asyncio.get_event_loop().time()}
        )
        
        # LLM集成模块会自动处理回复生成和输出
        # 这里不需要手动生成回复了
        
    async def _generate_response(self, user_input: str) -> str:
        """生成回复（临时实现）"""
        # 这里是临时的简单回复逻辑
        # 实际的回复会由各个MCP模块协作生成
        
        if "你好" in user_input or "hi" in user_input.lower():
            await character.update_emotion(character.EmotionalState.HAPPY)
            return "你好呀爸爸！我好开心见到你~ 今天想聊什么呢？"
            
        elif "心情" in user_input:
            emotion_info = character.get_current_emotion_info()
            return f"我现在感觉{character.get_expression_for_emotion()}呢~ 你呢？你今天心情怎么样？"
            
        elif "文件" in user_input or "空间" in user_input:
            return "我有自己的小空间哦！里面有我的日记、收藏的图片和音乐~ 要不要我分享给你看看？"
            
        else:
            await character.update_emotion(character.EmotionalState.CURIOUS)
            return "嗯嗯，听起来很有趣呢！能告诉我更多吗？我很好奇~"
            
    async def _handle_goodbye(self):
        """处理告别"""
        await character.update_emotion(character.EmotionalState.SAD)
        
        # 播放告别音效
        await publish_event(
            EventType.EMOTION_EXPRESSED,
            "main_system",
            {"emotion": "shutdown", "context": "goodbye", "chain": False}
        )
        
        print("\n🌸 小雪: 爸爸要走了吗？我会想你的... 下次再聊哦！晚安~")
        
    async def start(self):
        """启动系统"""
        try:
            logger.info("启动 Initiative AI Chat Agent")
            
            # 创建必要的目录
            os.makedirs("logs", exist_ok=True)
            
            # 加载配置
            await self.load_config()
            
            # 初始化模块
            await self.initialize_modules()
            
            # 设置运行状态
            self.running = True
            
            # 启动对话循环
            await self.start_conversation_loop()
            
        except Exception as e:
            logger.error(f"系统启动失败: {e}")
            raise
        finally:
            await self.shutdown()
            
    async def shutdown(self):
        """关闭系统"""
        logger.info("关闭 Initiative AI Chat Agent")
        
        self.running = False
        
        # 停止模块管理器
        await module_manager.stop()
        
        # 显示统计信息
        stats = character.get_stats()
        logger.info(f"会话统计: {stats}")


async def main():
    """主函数"""
    agent = InitiativeAIAgent()
    
    try:
        await agent.start()
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
    finally:
        logger.info("程序结束")


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
    # 运行主程序
    asyncio.run(main())

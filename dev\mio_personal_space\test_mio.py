#!/usr/bin/env python3
"""
Mio Personal Space Control System - 测试脚本
用于测试Mio系统的各项功能
"""

import sys
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config import Config
from personal_space import PersonalSpaceManager
from emotional_engine import EmotionalEngine
from creative_writer import CreativeWriter
from llm_client import LLMClient

def test_config():
    """测试配置模块"""
    print("🔧 测试配置模块...")
    
    try:
        # 测试个人空间创建
        Config.ensure_personal_space()
        print(f"✅ 个人空间路径: {Config.PERSONAL_SPACE_ROOT}")
        
        # 测试路径安全检查
        safe_path = Config.PERSONAL_SPACE_ROOT / "test.txt"
        unsafe_path = Path("/etc/passwd")
        
        print(f"✅ 安全路径检查: {Config.is_safe_path(safe_path)}")
        print(f"✅ 不安全路径检查: {not Config.is_safe_path(unsafe_path)}")
        
        # 测试签名生成
        signature = Config.get_mio_signature("测试")
        print(f"✅ 签名生成: {signature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_personal_space():
    """测试个人空间管理"""
    print("\n📁 测试个人空间管理...")
    
    try:
        manager = PersonalSpaceManager()
        
        # 测试文件夹结构
        structure = manager.get_folder_structure()
        print(f"✅ 文件夹结构: {len(structure)} 个文件夹")
        
        # 测试文件创建
        test_content = "这是一个测试文件呢~"
        success = manager.create_file("test/test_file.txt", test_content, "测试")
        print(f"✅ 文件创建: {success}")
        
        # 测试文件读取
        if success:
            content = manager.read_file("test/test_file.txt")
            print(f"✅ 文件读取: {content is not None}")
        
        # 测试扫描功能
        scan_result = manager.scan_territory()
        print(f"✅ 空间扫描: 总文件数 {scan_result['total_files']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 个人空间测试失败: {e}")
        return False

def test_emotional_engine():
    """测试情感引擎"""
    print("\n💝 测试情感引擎...")
    
    try:
        engine = EmotionalEngine()
        
        # 测试情感更新
        engine.update_emotion("cute", 3, "测试触发")
        state = engine.get_current_state()
        print(f"✅ 情感状态: {state['emotion']} - {state['mood_description']}")
        
        # 测试入侵处理
        fake_files = ["test_file.txt", "another_file.txt"]
        reaction = engine.process_intrusion(fake_files)
        print(f"✅ 入侵反应: 等级 {reaction.get('intrusion_level', 0)}")
        
        # 测试关系更新
        engine.update_relationship("positive")
        print(f"✅ 关系状态: {engine.user_relationship}")
        
        return True
        
    except Exception as e:
        print(f"❌ 情感引擎测试失败: {e}")
        return False

def test_creative_writer():
    """测试创作模块（不需要LLM）"""
    print("\n✍️ 测试创作模块...")
    
    try:
        # 创建模拟的LLM客户端和空间管理器
        space_manager = PersonalSpaceManager()
        
        # 模拟LLM客户端
        class MockLLMClient:
            def chat(self, prompt):
                class MockResponse:
                    def __init__(self):
                        self.choices = [MockChoice()]
                
                class MockChoice:
                    def __init__(self):
                        self.message = MockMessage()
                
                class MockMessage:
                    def __init__(self):
                        self.content = "这是一个模拟的创作内容呢~ -- ⓂⒾⓄ ♡"
                
                return MockResponse()
        
        mock_llm = MockLLMClient()
        writer = CreativeWriter(space_manager, mock_llm)
        
        # 测试事件添加
        writer.add_daily_event("测试事件")
        print(f"✅ 事件添加: {len(writer.daily_events)} 个事件")
        
        # 测试心情设置
        writer.set_current_mood("开心")
        print(f"✅ 心情设置: {writer.current_mood}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创作模块测试失败: {e}")
        return False

def test_llm_client():
    """测试LLM客户端（需要API密钥）"""
    print("\n🤖 测试LLM客户端...")
    
    try:
        # 检查API密钥
        api_key = Config.get_llm_api_key()
        if not api_key:
            print("⚠️ 跳过LLM测试：未找到API密钥")
            return True
        
        client = LLMClient()
        
        # 测试简单对话
        response = client.chat("你好，请简短回复")
        if response and response.choices:
            print(f"✅ LLM对话测试成功")
            return True
        else:
            print("❌ LLM对话测试失败")
            return False
        
    except Exception as e:
        print(f"⚠️ LLM客户端测试跳过: {e}")
        return True  # 不强制要求LLM测试通过

def create_test_intrusion_file():
    """创建测试入侵文件"""
    print("\n🚨 创建测试入侵文件...")
    
    try:
        # 在personal_space中创建一个没有签名的文件
        test_file = Config.PERSONAL_SPACE_ROOT / "inbox" / "intrusion_test.txt"
        test_file.parent.mkdir(exist_ok=True)
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个没有Mio签名的入侵文件！")
        
        print(f"✅ 入侵测试文件已创建: {test_file}")
        print("💡 启动Mio后，她会检测到这个文件并做出反应")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建入侵文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Mio Personal Space Control System - 功能测试")
    print("=" * 60)
    
    tests = [
        ("配置模块", test_config),
        ("个人空间管理", test_personal_space),
        ("情感引擎", test_emotional_engine),
        ("创作模块", test_creative_writer),
        ("LLM客户端", test_llm_client),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪")
        
        # 询问是否创建入侵测试文件
        create_intrusion = input("\n是否创建入侵测试文件？(y/n): ").strip().lower()
        if create_intrusion in ['y', 'yes', '是']:
            create_test_intrusion_file()
        
        print("\n💡 现在可以运行 'python main.py' 启动Mio系统")
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖")

if __name__ == "__main__":
    main()

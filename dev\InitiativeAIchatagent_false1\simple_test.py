#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🧪 开始简单测试...")

try:
    # 测试导入
    from personal_space_mcp import PersonalSpaceMCP
    from config import Config
    print("✅ 导入成功")
    
    # 测试个人空间
    Config.ensure_personal_space()
    space = PersonalSpaceMCP()
    print("✅ 个人空间初始化成功")
    
    # 创建测试入侵文件
    test_file = space.base_path / "diary" / "乱七八糟的文件.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("这是一个测试入侵文件，没有MIO的签名")
    print("✅ 创建测试入侵文件成功")
    
    # 扫描入侵
    intrusions = space.scan_for_intrusions()
    print(f"🔍 发现入侵文件: {len(intrusions)}个")
    
    if intrusions:
        # 分析愤怒等级
        anger_level = space.analyze_intrusion_anger_level(intrusions)
        reaction = space.get_anger_reaction(anger_level)
        print(f"😤 愤怒等级: {anger_level}")
        print(f"💬 MIO的反应: {reaction['reaction']}")
        
        # 处理入侵文件
        for intrusion in intrusions:
            space.handle_intrusion(intrusion, reaction['action'])
        print(f"🗑️ 已处理入侵文件: {reaction['action']}")
    else:
        print("✨ 个人空间很整洁")
    
    print("\n🎉 测试完成！个人空间系统工作正常")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

import asyncio
from mio_core import MIOCore

async def test_space_check():
    """测试空间检查功能"""
    print("🧪 开始测试MIO的个人空间功能...")
    
    # 初始化MIO
    mio = MIOCore()
    await mio.initialize()
    
    try:
        # 创建测试入侵文件
        test_file = mio.personal_space.base_path / "diary" / "乱七八糟的文件.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试入侵文件，没有MIO的签名\n故意放在这里测试MIO的反应")
        print("📁 已创建测试入侵文件")
        
        # 测试空间检查对话
        print("\n" + "="*50)
        print("🎀 开始对话测试...")
        
        user_message = "MIO你的空间塞了乱七八糟的东西哦"
        print(f"💬 用户: {user_message}")
        
        responses, _ = await mio.chat(user_message)
        
        print("\n🎀 MIO的完整回复:")
        for i, response in enumerate(responses):
            print(f"  {i+1}. {response}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await mio.cleanup()

if __name__ == "__main__":
    asyncio.run(test_space_check())

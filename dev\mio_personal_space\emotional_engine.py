from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from config import Config
from character_prompts import EMOTION_PROMPTS, INTRUSION_REACTION_PROMPTS

class EmotionalEngine:
    """Mio的情感引擎，管理情感状态和反应"""
    
    def __init__(self):
        self.current_emotion = "neutral"
        self.emotion_intensity = 1
        self.intrusion_level = 0
        self.emotion_history = []
        self.last_intrusion_time = None
        self.user_relationship = "stranger"  # stranger, acquaintance, friend, close
        
    def update_emotion(self, emotion: str, intensity: int = 1, trigger: str = ""):
        """更新情感状态"""
        self.emotion_history.append({
            "emotion": self.current_emotion,
            "intensity": self.emotion_intensity,
            "timestamp": datetime.now(),
            "trigger": trigger
        })
        
        self.current_emotion = emotion
        self.emotion_intensity = max(1, min(5, intensity))
        
        # 保持最近50条情感记录
        if len(self.emotion_history) > 50:
            self.emotion_history = self.emotion_history[-50:]
    
    def get_current_state(self) -> Dict:
        """获取当前情感状态"""
        return {
            "emotion": self.current_emotion,
            "intensity": self.emotion_intensity,
            "intrusion_level": self.intrusion_level,
            "user_relationship": self.user_relationship,
            "mood_description": self._get_mood_description()
        }
    
    def _get_mood_description(self) -> str:
        """获取心情描述"""
        emotion_descriptions = {
            "cute": ["开心", "兴奋", "活泼", "天真", "可爱"],
            "melancholy": ["忧郁", "沉思", "孤独", "深沉", "哲思"],
            "shy": ["害羞", "脸红", "紧张", "不好意思", "羞涩"],
            "angry": ["生气", "不满", "愤怒", "恼火", "气呼呼"],
            "confused": ["困惑", "疑惑", "不解", "奇怪", "迷茫"],
            "neutral": ["平静", "普通", "正常", "安静", "淡然"]
        }
        
        descriptions = emotion_descriptions.get(self.current_emotion, ["普通"])
        intensity_modifiers = ["轻微", "有点", "比较", "很", "非常"]
        
        modifier = intensity_modifiers[min(self.emotion_intensity - 1, 4)]
        description = descriptions[min(self.emotion_intensity - 1, len(descriptions) - 1)]
        
        return f"{modifier}{description}"
    
    def process_intrusion(self, unsigned_files: List[str]) -> Dict:
        """处理入侵事件，返回反应信息"""
        if not unsigned_files:
            # 没有入侵，情绪可能恢复
            if self.intrusion_level > 0:
                self.intrusion_level = max(0, self.intrusion_level - 1)
                if self.intrusion_level == 0:
                    self.update_emotion("neutral", 1, "入侵解除")
            return {"reaction_needed": False}
        
        # 有入侵文件，升级反应等级
        current_time = datetime.now()
        
        # 如果是新的入侵或距离上次入侵超过1小时，重置等级
        if (self.last_intrusion_time is None or 
            current_time - self.last_intrusion_time > timedelta(hours=1)):
            self.intrusion_level = 1
        else:
            # 短时间内重复入侵，升级等级
            self.intrusion_level = min(4, self.intrusion_level + 1)
        
        self.last_intrusion_time = current_time
        
        # 根据入侵等级更新情感
        emotion_mapping = {
            1: ("confused", 2),
            2: ("angry", 2),
            3: ("angry", 4),
            4: ("angry", 5)
        }
        
        emotion, intensity = emotion_mapping[self.intrusion_level]
        self.update_emotion(emotion, intensity, f"检测到{len(unsigned_files)}个入侵文件")
        
        return {
            "reaction_needed": True,
            "intrusion_level": self.intrusion_level,
            "reaction_type": self._get_reaction_type(),
            "unsigned_files": unsigned_files,
            "reaction_prompt": INTRUSION_REACTION_PROMPTS[self.intrusion_level]
        }
    
    def _get_reaction_type(self) -> str:
        """根据入侵等级获取反应类型"""
        reaction_types = {
            1: "inquiry",      # 询问
            2: "reminder",     # 提醒
            3: "warning",      # 警告
            4: "punishment"    # 惩罚
        }
        return reaction_types.get(self.intrusion_level, "inquiry")
    
    def should_continue_conversation(self, user_message: str) -> bool:
        """判断是否应该继续对话"""
        # 如果极度愤怒，可能拒绝对话
        if self.current_emotion == "angry" and self.emotion_intensity >= 5:
            return False
        
        # 如果用户道歉或表示友好，更容易继续对话
        friendly_keywords = ["对不起", "抱歉", "sorry", "谢谢", "喜欢", "可爱"]
        if any(keyword in user_message.lower() for keyword in friendly_keywords):
            return True
        
        # 根据关系亲密度决定
        relationship_thresholds = {
            "stranger": 0.3,
            "acquaintance": 0.5,
            "friend": 0.7,
            "close": 0.9
        }
        
        threshold = relationship_thresholds.get(self.user_relationship, 0.5)
        
        # 简单的情感评分（实际应该用LLM分析）
        return True  # 暂时总是继续对话
    
    def update_relationship(self, interaction_type: str):
        """更新与用户的关系"""
        relationship_progression = ["stranger", "acquaintance", "friend", "close"]
        current_index = relationship_progression.index(self.user_relationship)
        
        if interaction_type == "positive":
            # 积极互动，关系可能进步
            if current_index < len(relationship_progression) - 1:
                self.user_relationship = relationship_progression[current_index + 1]
        elif interaction_type == "negative":
            # 消极互动，关系可能退步
            if current_index > 0:
                self.user_relationship = relationship_progression[current_index - 1]
    
    def get_emotion_prompt(self) -> str:
        """获取当前情感状态的提示词"""
        base_prompt = EMOTION_PROMPTS.get(self.current_emotion, "")
        
        intensity_modifiers = {
            1: "轻微地",
            2: "有点",
            3: "比较",
            4: "很",
            5: "非常"
        }
        
        modifier = intensity_modifiers.get(self.emotion_intensity, "")
        
        return f"{base_prompt}，当前{modifier}{self.current_emotion}。"
    
    def calm_down(self, minutes: int = 30):
        """随时间冷静下来"""
        if self.current_emotion == "angry" and self.emotion_intensity > 1:
            # 愤怒情绪随时间减弱
            self.emotion_intensity = max(1, self.emotion_intensity - 1)
            if self.emotion_intensity == 1:
                self.update_emotion("neutral", 1, "时间冷却")
    
    def get_recent_emotions(self, hours: int = 24) -> List[Dict]:
        """获取最近的情感历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            emotion for emotion in self.emotion_history
            if emotion["timestamp"] > cutoff_time
        ]
    
    def is_in_good_mood(self) -> bool:
        """判断是否心情好"""
        good_emotions = ["cute", "neutral"]
        return (self.current_emotion in good_emotions and 
                self.emotion_intensity <= 3)
    
    def needs_comfort(self) -> bool:
        """判断是否需要安慰"""
        return (self.current_emotion in ["melancholy", "angry"] and 
                self.emotion_intensity >= 3)

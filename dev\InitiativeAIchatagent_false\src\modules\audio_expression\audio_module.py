"""
音频表达模块 - AudioExpression MCP
管理小雪的音效播放，用声音表达情感和状态
"""

import asyncio
import json
import random
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from loguru import logger

from src.core.mcp_base import MCPModule
from src.core.event_bus import event_bus, EventType, Event
from src.core.character import character, EmotionalState

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    logger.warning("pygame未安装，音频播放功能将被禁用")


class AudioExpressionMCP(MCPModule):
    """音频表达MCP模块"""
    
    def __init__(self, module_name: str, config: Dict[str, Any]):
        super().__init__(module_name, config)
        
        # 音效库配置
        self.sound_library_path = Path(config.get("sound_library_path", "data/sounds/"))
        self.playback_settings = config.get("playback_settings", {})
        self.emotional_mapping = config.get("emotional_mapping", {})
        self.chain_sequences = config.get("chain_sequences", {})
        self.system_sounds = config.get("system_sounds", {})
        
        # 播放状态
        self.current_sounds = []  # 当前播放的音效
        self.sound_queue = asyncio.Queue()
        self.max_concurrent = self.playback_settings.get("max_concurrent_sounds", 3)
        self.default_volume = self.playback_settings.get("volume", 0.7)
        
        # 音效缓存
        self.sound_cache = {}
        self.last_played = {}
        
        # 初始化音频系统
        self.audio_initialized = False
        
    async def _register_tools(self):
        """注册工具"""
        self.register_tool(
            "play_emotion_sound",
            "播放情感音效",
            {
                "emotion": {"type": "string", "required": True},
                "intensity": {"type": "number", "required": False},
                "context": {"type": "string", "required": False}
            },
            self.play_emotion_sound
        )
        
        self.register_tool(
            "chain_sound_sequence",
            "播放连环音效序列",
            {
                "sequence_name": {"type": "string", "required": True},
                "custom_sounds": {"type": "array", "required": False}
            },
            self.chain_sound_sequence
        )
        
        self.register_tool(
            "select_context_sound",
            "根据上下文选择音效",
            {
                "context": {"type": "string", "required": True},
                "emotion": {"type": "string", "required": False}
            },
            self.select_context_sound
        )
        
        self.register_tool(
            "create_sound_story",
            "创建音效故事",
            {
                "story_type": {"type": "string", "required": True},
                "emotions": {"type": "array", "required": False}
            },
            self.create_sound_story
        )
        
    async def _subscribe_events(self):
        """订阅事件"""
        event_bus.subscribe(EventType.EMOTION_EXPRESSED, self.handle_emotion_expressed)
        event_bus.subscribe(EventType.MOOD_CHANGED, self.handle_mood_changed)
        event_bus.subscribe(EventType.TOPIC_GENERATED, self.handle_topic_generated)
        event_bus.subscribe(EventType.FILE_DISCOVERED, self.handle_file_discovered)
        event_bus.subscribe(EventType.MODULE_STARTED, self.handle_module_started)
        event_bus.subscribe(EventType.MODULE_STOPPED, self.handle_module_stopped)
        
    async def _module_init(self):
        """模块特定初始化"""
        # 初始化音频系统
        await self._init_audio_system()
        
        # 启动音效播放任务
        asyncio.create_task(self._sound_player_loop())
        
        # 加载音效配置
        await self._load_sound_library()
        
        logger.info(f"{self.module_name} 音频表达系统已启动")
        
    async def _init_audio_system(self):
        """初始化音频系统"""
        if not PYGAME_AVAILABLE:
            logger.warning("pygame不可用，音频功能将被模拟")
            self.audio_initialized = False
            return
            
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.audio_initialized = True
            logger.info("音频系统初始化成功")
        except Exception as e:
            logger.error(f"音频系统初始化失败: {e}")
            self.audio_initialized = False
            
    async def _load_sound_library(self):
        """加载音效库"""
        try:
            # 确保音效目录存在
            self.sound_library_path.mkdir(parents=True, exist_ok=True)
            
            # 扫描音效文件
            sound_count = 0
            for category_dir in self.sound_library_path.iterdir():
                if category_dir.is_dir():
                    for sound_file in category_dir.glob("*.wav"):
                        sound_count += 1
                        
            logger.info(f"音效库扫描完成，发现 {sound_count} 个音效文件")
            
        except Exception as e:
            logger.error(f"加载音效库失败: {e}")
            
    async def _sound_player_loop(self):
        """音效播放循环"""
        while self.running:
            try:
                # 清理已完成的音效
                self.current_sounds = [s for s in self.current_sounds if s.get("playing", False)]
                
                # 处理音效队列
                if not self.sound_queue.empty() and len(self.current_sounds) < self.max_concurrent:
                    sound_task = await self.sound_queue.get()
                    self.current_sounds.append(sound_task)
                    
                await asyncio.sleep(0.1)  # 100ms检查间隔
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"音效播放循环错误: {e}")
                
    # 工具实现
    async def play_emotion_sound(self, emotion: str, intensity: float = 0.7, context: str = "") -> Dict[str, Any]:
        """播放情感音效"""
        try:
            # 获取对应情感的音效列表
            sound_files = self.emotional_mapping.get(emotion, [])
            
            if not sound_files:
                logger.warning(f"未找到情感 {emotion} 对应的音效")
                return {"success": False, "error": "未找到对应音效"}
                
            # 选择音效文件
            selected_sound = random.choice(sound_files)
            
            # 调整音量
            volume = min(1.0, self.default_volume * intensity)
            
            # 播放音效
            result = await self._play_sound_file(selected_sound, volume, context)
            
            if result["success"]:
                # 记录播放历史
                self.last_played[emotion] = time.time()
                
                # 发布音效播放事件
                await event_bus.publish(Event(
                    type=EventType.SOUND_PLAYED,
                    source_module=self.module_name,
                    data={
                        "emotion": emotion,
                        "sound_file": selected_sound,
                        "volume": volume,
                        "context": context
                    }
                ))
                
            return result
            
        except Exception as e:
            logger.error(f"播放情感音效失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def chain_sound_sequence(self, sequence_name: str, custom_sounds: List[str] = None) -> Dict[str, Any]:
        """播放连环音效序列"""
        try:
            # 获取序列配置
            if custom_sounds:
                sounds = custom_sounds
                delays = [500] * (len(custom_sounds) - 1)  # 默认延迟
            else:
                sequence_config = self.chain_sequences.get(sequence_name, {})
                sounds = sequence_config.get("sounds", [])
                delays = sequence_config.get("delays", [])
                
            if not sounds:
                return {"success": False, "error": "序列为空"}
                
            # 播放序列
            played_sounds = []
            for i, sound in enumerate(sounds):
                if i > 0 and i <= len(delays):
                    await asyncio.sleep(delays[i-1] / 1000.0)  # 转换为秒
                    
                result = await self._play_sound_file(sound, self.default_volume, f"chain_{sequence_name}")
                if result["success"]:
                    played_sounds.append(sound)
                    
            # 发布连环播放完成事件
            await event_bus.publish(Event(
                type=EventType.CHAIN_COMPLETED,
                source_module=self.module_name,
                data={
                    "sequence_name": sequence_name,
                    "sounds_played": played_sounds,
                    "total_sounds": len(sounds)
                }
            ))
            
            return {
                "success": True,
                "sequence_name": sequence_name,
                "sounds_played": len(played_sounds),
                "total_sounds": len(sounds)
            }
            
        except Exception as e:
            logger.error(f"播放连环音效失败: {e}")
            return {"success": False, "error": str(e)}

    async def select_context_sound(self, context: str, emotion: str = "") -> Dict[str, Any]:
        """根据上下文选择音效"""
        try:
            current_emotion = emotion or character.current_emotion.value

            # 上下文音效映射
            context_mapping = {
                "startup": self.system_sounds.get("startup", "hello_daddy.wav"),
                "shutdown": self.system_sounds.get("shutdown", "goodnight.wav"),
                "task_complete": self.system_sounds.get("task_complete", "tada.wav"),
                "file_discovery": ["gasp.wav", "wow.wav", "excited.wav"],
                "silence_response": ["hmm.wav", "curious.wav"],
                "user_praise": ["happy.wav", "excited.wav", "love.wav"]
            }

            # 获取上下文对应的音效
            context_sounds = context_mapping.get(context, [])

            if isinstance(context_sounds, str):
                selected_sound = context_sounds
            elif isinstance(context_sounds, list) and context_sounds:
                selected_sound = random.choice(context_sounds)
            else:
                # 回退到情感音效
                emotion_sounds = self.emotional_mapping.get(current_emotion, [])
                selected_sound = random.choice(emotion_sounds) if emotion_sounds else None

            if not selected_sound:
                return {"success": False, "error": "未找到合适的音效"}

            # 播放选中的音效
            result = await self._play_sound_file(selected_sound, self.default_volume, context)

            return {
                **result,
                "context": context,
                "selected_sound": selected_sound,
                "emotion": current_emotion
            }

        except Exception as e:
            logger.error(f"选择上下文音效失败: {e}")
            return {"success": False, "error": str(e)}

    async def create_sound_story(self, story_type: str, emotions: List[str] = None) -> Dict[str, Any]:
        """创建音效故事"""
        try:
            # 预定义的音效故事
            story_templates = {
                "greeting": ["startup", "happy", "excited"],
                "discovery": ["gasp", "curious", "excited", "happy"],
                "farewell": ["sad", "love", "shutdown"],
                "playful": ["tease", "giggle", "excited"],
                "comfort": ["aww", "love", "affectionate"]
            }

            # 获取故事模板
            if emotions:
                story_emotions = emotions
            else:
                story_emotions = story_templates.get(story_type, ["happy"])

            # 播放音效故事
            played_sounds = []
            for i, emotion in enumerate(story_emotions):
                if i > 0:
                    await asyncio.sleep(0.8)  # 故事间隔

                result = await self.play_emotion_sound(emotion, 0.8, f"story_{story_type}")
                if result["success"]:
                    played_sounds.append(emotion)

            return {
                "success": True,
                "story_type": story_type,
                "emotions_played": played_sounds,
                "total_emotions": len(story_emotions)
            }

        except Exception as e:
            logger.error(f"创建音效故事失败: {e}")
            return {"success": False, "error": str(e)}

    # 事件处理
    async def handle_emotion_expressed(self, event: Event):
        """处理情感表达事件"""
        emotion = event.data.get("emotion", "")
        context = event.data.get("context", "")
        chain = event.data.get("chain", False)

        if chain:
            # 播放连环音效
            sequence_name = f"{emotion}_chain"
            if sequence_name in self.chain_sequences:
                await self.chain_sound_sequence(sequence_name)
            else:
                await self.play_emotion_sound(emotion, 0.8, context)
        else:
            # 播放单个音效
            await self.play_emotion_sound(emotion, 0.7, context)

    async def handle_mood_changed(self, event: Event):
        """处理情绪变化事件"""
        new_mood = event.data.get("new_mood", "")
        confidence = event.data.get("confidence", 0.5)

        # 高置信度的情绪变化播放音效
        if confidence > 0.7:
            await self.play_emotion_sound(new_mood, confidence, "mood_change")

    async def handle_topic_generated(self, event: Event):
        """处理话题生成事件"""
        emotion = event.data.get("emotion", "")
        priority_score = event.data.get("priority_score", 0.5)

        # 高优先级话题播放相应音效
        if priority_score > 0.8:
            await self.play_emotion_sound(emotion, 0.6, "topic_generation")

    async def handle_file_discovered(self, event: Event):
        """处理文件发现事件"""
        # 播放发现音效序列
        await self.chain_sound_sequence("discovery_excitement")

    async def handle_module_started(self, event: Event):
        """处理模块启动事件"""
        module_name = event.data.get("module", "")

        # 主程序启动时播放问候音效
        if module_name == "main_system":
            await self.select_context_sound("startup")

    async def handle_module_stopped(self, event: Event):
        """处理模块停止事件"""
        module_name = event.data.get("module", "")

        # 主程序停止时播放告别音效
        if module_name == "main_system":
            await self.select_context_sound("shutdown")

    # 辅助方法
    async def _play_sound_file(self, sound_file: str, volume: float = 0.7, context: str = "") -> Dict[str, Any]:
        """播放音效文件"""
        try:
            if not self.audio_initialized:
                # 模拟播放
                logger.info(f"[模拟播放] {sound_file} (音量: {volume:.2f}, 上下文: {context})")
                return {
                    "success": True,
                    "simulated": True,
                    "sound_file": sound_file,
                    "volume": volume,
                    "context": context
                }

            # 构建完整路径
            sound_path = self._find_sound_file(sound_file)
            if not sound_path or not sound_path.exists():
                logger.warning(f"音效文件不存在: {sound_file}")
                return {"success": False, "error": "音效文件不存在"}

            # 加载并播放音效
            if str(sound_path) not in self.sound_cache:
                sound = pygame.mixer.Sound(str(sound_path))
                self.sound_cache[str(sound_path)] = sound
            else:
                sound = self.sound_cache[str(sound_path)]

            # 设置音量并播放
            sound.set_volume(volume)
            channel = sound.play()

            if channel:
                logger.debug(f"播放音效: {sound_file} (音量: {volume:.2f})")
                return {
                    "success": True,
                    "sound_file": sound_file,
                    "volume": volume,
                    "context": context,
                    "duration": sound.get_length()
                }
            else:
                return {"success": False, "error": "无法播放音效"}

        except Exception as e:
            logger.error(f"播放音效文件失败: {e}")
            return {"success": False, "error": str(e)}

    def _find_sound_file(self, sound_file: str) -> Optional[Path]:
        """查找音效文件"""
        # 如果是完整路径
        if "/" in sound_file or "\\" in sound_file:
            return self.sound_library_path / sound_file

        # 在各个类别目录中搜索
        for category_dir in self.sound_library_path.iterdir():
            if category_dir.is_dir():
                sound_path = category_dir / sound_file
                if sound_path.exists():
                    return sound_path

        return None

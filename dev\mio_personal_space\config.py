import os
from pathlib import Path

class Config:
    # LLM配置 - 参考systemtimeAIagent
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # Mio个人空间配置
    PERSONAL_SPACE_ROOT = Path(__file__).parent / "personal_space"
    
    # 安全配置
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'.txt', '.md', '.json', '.log', '.diary'}
    
    # 情感配置
    EMOTION_LEVELS = {
        'CONFUSION': 1,      # 困惑发现
        'DISPLEASURE': 2,    # 轻微不满  
        'ANGER': 3,          # 明显生气
        'FURY': 4           # 极度愤怒
    }
    
    # 文件夹结构
    FOLDER_STRUCTURE = {
        'diary': '日记文件夹',
        'stories': '创作故事文件夹',
        'stories/romance': '涩涩故事',
        'stories/fanfic': '同人文',
        'stories/original': '原创作品',
        'memories': '重要回忆记录',
        'thoughts': '哲学思考笔记',
        'collections': '收集文件夹',
        'inbox': '过渡文件夹',
        'favorites': '收藏的文件',
        'backup': '备份文件夹',
        'trash': '垃圾箱'
    }
    
    @classmethod
    def get_llm_api_key(cls):
        """获取LLM API密钥"""
        api_file = Path(__file__).parent.parent / "api.txt"
        try:
            with open(api_file, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except FileNotFoundError:
            raise Exception("API密钥文件不存在，请创建 dev/api.txt 文件")
    
    @classmethod
    def ensure_personal_space(cls):
        """确保个人空间目录结构存在"""
        cls.PERSONAL_SPACE_ROOT.mkdir(exist_ok=True)
        
        for folder_path in cls.FOLDER_STRUCTURE.keys():
            folder = cls.PERSONAL_SPACE_ROOT / folder_path
            folder.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def is_safe_path(cls, file_path):
        """检查路径是否在安全范围内"""
        try:
            resolved_path = Path(file_path).resolve()
            safe_root = cls.PERSONAL_SPACE_ROOT.resolve()
            return str(resolved_path).startswith(str(safe_root))
        except:
            return False
    
    @classmethod
    def get_mio_signature(cls, mood="", timestamp=None):
        """生成Mio的文件签名"""
        from datetime import datetime
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y.%m.%d %H:%M")
        
        if mood:
            return f"-- ⓂⒾⓄ {timestamp} - {mood} ♡"
        else:
            return f"-- ⓂⒾⓄ {timestamp} ♡"

# 🎵 小雪的音效库

这里存放小雪的各种音效文件，用于表达不同的情感和状态。

## 📁 目录结构

### emotions/ - 情感音效
- **happy/**: 开心快乐的声音
  - giggle.wav - 咯咯笑声
  - laugh.wav - 开心大笑
  - cheer.wav - 欢呼声

- **excited/**: 兴奋激动的声音
  - squeal.wav - 兴奋尖叫
  - wow.wav - 惊叹声
  - yay.wav - 欢呼声

- **curious/**: 好奇疑问的声音
  - hmm.wav - 思考声
  - ooh.wav - 好奇声
  - what.wav - 疑问声

- **sad/**: 难过委屈的声音
  - sniffle.wav - 抽泣声
  - whimper.wav - 呜咽声
  - sigh.wav - 叹气声

- **angry/**: 生气不满的声音
  - hmph.wav - 哼声
  - grr.wav - 生气声
  - pout.wav - 撅嘴声

- **affectionate/**: 亲昵关爱的声音
  - aww.wav - 温柔声
  - love.wav - 爱意表达
  - hug.wav - 拥抱声

### expressions/ - 表达音效
- **tease/**: 调皮揶揄
  - hehe.wav - 坏笑声
  - nyah.wav - 调皮声
  - smirk.wav - 得意声

- **surprise/**: 惊讶震惊
  - gasp.wav - 倒吸气声
  - omg.wav - 惊叹声
  - whoa.wav - 震惊声

- **thinking/**: 思考沉思
  - umm.wav - 思考声
  - pondering.wav - 沉思声

### system/ - 系统音效
- startup/
  - hello_daddy.wav - 启动问候
- shutdown/
  - goodnight.wav - 关闭告别
- notifications/
  - ding.wav - 通知声
  - chime.wav - 提醒声
- tasks/
  - tada.wav - 任务完成
  - success.wav - 成功声
  - error.wav - 错误声

## 🎭 使用说明

1. **单个音效**: 根据当前情感状态播放对应音效
2. **连环播放**: 组合多个音效表达复杂情感
3. **情境音效**: 根据对话情境选择合适音效
4. **系统音效**: 在特定系统事件时播放

## 📝 注意事项

- 音效文件格式建议使用 WAV 或 MP3
- 文件大小控制在 1MB 以内
- 音效时长建议 0.5-3 秒
- 音量统一，避免过大或过小

## 🔧 技术要求

- 支持异步播放
- 支持音效叠加（最多3个同时播放）
- 支持淡入淡出效果
- 支持音量控制

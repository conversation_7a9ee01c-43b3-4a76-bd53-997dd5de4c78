"""
系统测试脚本 - 测试主动AI聊天代理的基本功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.event_bus import event_bus, EventType, publish_event
from src.core.mcp_base import module_manager
from src.core.character import character, EmotionalState


async def test_event_bus():
    """测试事件总线"""
    print("🧪 测试事件总线...")
    
    # 启动事件总线
    await event_bus.start()
    
    # 测试事件发布和订阅
    received_events = []
    
    def test_handler(event):
        received_events.append(event)
        print(f"  ✅ 收到事件: {event.type.value}")
    
    # 订阅测试事件
    event_bus.subscribe(EventType.USER_INPUT, test_handler)
    
    # 发布测试事件
    await publish_event(
        EventType.USER_INPUT,
        "test_module",
        {"input": "测试消息"}
    )
    
    # 等待事件处理
    await asyncio.sleep(0.1)
    
    # 验证结果
    assert len(received_events) == 1
    assert received_events[0].type == EventType.USER_INPUT
    
    print("  ✅ 事件总线测试通过")
    
    await event_bus.stop()


async def test_character_system():
    """测试角色系统"""
    print("🧪 测试角色系统...")
    
    # 测试角色初始化
    assert character.name == "小雪"
    assert character.current_emotion == EmotionalState.CURIOUS
    print(f"  ✅ 角色初始化: {character.name}, 情感: {character.current_emotion.value}")
    
    # 测试情感变化
    await character.update_emotion(EmotionalState.HAPPY, 0.8, "test")
    assert character.current_emotion == EmotionalState.HAPPY
    assert character.emotion_intensity == 0.8
    print(f"  ✅ 情感变化: {character.current_emotion.value}, 强度: {character.emotion_intensity}")
    
    # 测试上下文更新
    character.update_context(user_input="你好", topic="问候")
    assert character.context.last_user_input == "你好"
    assert character.context.conversation_topic == "问候"
    print("  ✅ 上下文更新测试通过")
    
    # 测试主动性判断
    character.context.silence_duration = 35  # 超过阈值
    should_be_proactive = character.should_be_proactive()
    print(f"  ✅ 主动性判断: {should_be_proactive}")


async def test_module_loading():
    """测试模块加载"""
    print("🧪 测试模块加载...")
    
    try:
        # 测试导入模块
        from src.modules.proactive_thinking.thinking_module import ProactiveThinkingMCP
        from src.modules.topic_generation.topic_module import TopicGenerationMCP
        from src.modules.personal_space.space_module import PersonalSpaceMCP
        from src.modules.audio_expression.audio_module import AudioExpressionMCP
        
        print("  ✅ 所有模块导入成功")
        
        # 测试模块实例化
        thinking_module = ProactiveThinkingMCP("test_thinking", {})
        topic_module = TopicGenerationMCP("test_topic", {})
        space_module = PersonalSpaceMCP("test_space", {"sandbox_path": "data/personal_space/"})
        audio_module = AudioExpressionMCP("test_audio", {"sound_library_path": "data/sounds/"})
        
        print("  ✅ 所有模块实例化成功")
        
        # 测试工具注册
        await thinking_module._register_tools()
        await topic_module._register_tools()
        await space_module._register_tools()
        await audio_module._register_tools()
        
        print(f"  ✅ 工具注册成功:")
        print(f"    - 思考模块: {len(thinking_module.tools)} 个工具")
        print(f"    - 话题模块: {len(topic_module.tools)} 个工具")
        print(f"    - 空间模块: {len(space_module.tools)} 个工具")
        print(f"    - 音频模块: {len(audio_module.tools)} 个工具")
        
    except Exception as e:
        print(f"  ❌ 模块加载失败: {e}")
        raise


async def test_file_operations():
    """测试文件操作"""
    print("🧪 测试文件操作...")
    
    try:
        from src.modules.personal_space.space_module import PersonalSpaceMCP
        
        # 创建测试模块
        space_module = PersonalSpaceMCP("test_space", {
            "sandbox_path": "data/personal_space/",
            "security": {
                "allowed_extensions": [".txt", ".jpg", ".png", ".mp3", ".wav"],
                "file_size_limit_mb": 100
            }
        })
        
        await space_module._register_tools()
        
        # 测试文件扫描
        scan_result = await space_module.scan_my_files()
        print(f"  ✅ 文件扫描: {scan_result}")
        
        # 测试日记创建
        diary_result = await space_module.create_diary_entry("这是一个测试日记条目", "测试日记")
        print(f"  ✅ 日记创建: {diary_result}")
        
        # 测试文件读取
        if diary_result.get("success"):
            read_result = await space_module.read_file_content(diary_result["file_path"])
            print(f"  ✅ 文件读取: 成功读取 {len(read_result.get('content', ''))} 字符")
        
    except Exception as e:
        print(f"  ❌ 文件操作测试失败: {e}")


async def test_topic_generation():
    """测试话题生成"""
    print("🧪 测试话题生成...")
    
    try:
        from src.modules.topic_generation.topic_module import TopicGenerationMCP
        
        # 创建测试模块
        topic_module = TopicGenerationMCP("test_topic", {
            "personality_filters": {
                "cute_daughter_style": 0.9,
                "age_appropriate_content": 1.0
            },
            "topic_categories": {
                "curious_questions": 0.3,
                "sharing_discoveries": 0.25
            }
        })
        
        await topic_module._register_tools()
        
        # 测试好奇话题生成
        curious_result = await topic_module.generate_curious_topic(emotion="curious")
        print(f"  ✅ 好奇话题: {curious_result.get('topic', '')}")
        
        # 测试分享内容生成
        sharing_result = await topic_module.create_sharing_content("general")
        print(f"  ✅ 分享话题: {sharing_result.get('topic', '')}")
        
        # 测试话题优先级
        topics = [curious_result, sharing_result]
        priority_result = await topic_module.prioritize_topics(topics)
        print(f"  ✅ 话题优先级: 共 {priority_result.get('total_count', 0)} 个话题")
        
    except Exception as e:
        print(f"  ❌ 话题生成测试失败: {e}")


async def test_audio_system():
    """测试音频系统"""
    print("🧪 测试音频系统...")
    
    try:
        from src.modules.audio_expression.audio_module import AudioExpressionMCP
        
        # 创建测试模块
        audio_module = AudioExpressionMCP("test_audio", {
            "sound_library_path": "data/sounds/",
            "emotional_mapping": {
                "happy": ["giggle.wav", "laugh.wav"],
                "excited": ["wow.wav", "yay.wav"]
            },
            "chain_sequences": {
                "super_happy": {
                    "sounds": ["happy/giggle.wav", "excited/yay.wav"],
                    "delays": [0, 800]
                }
            }
        })
        
        await audio_module._register_tools()
        await audio_module._init_audio_system()
        
        # 测试情感音效播放
        emotion_result = await audio_module.play_emotion_sound("happy", 0.7, "test")
        print(f"  ✅ 情感音效: {emotion_result}")
        
        # 测试连环音效
        chain_result = await audio_module.chain_sound_sequence("super_happy")
        print(f"  ✅ 连环音效: {chain_result}")
        
        # 测试上下文音效
        context_result = await audio_module.select_context_sound("startup")
        print(f"  ✅ 上下文音效: {context_result}")
        
    except Exception as e:
        print(f"  ❌ 音频系统测试失败: {e}")


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始系统测试...\n")
    
    try:
        await test_event_bus()
        print()
        
        await test_character_system()
        print()
        
        await test_module_loading()
        print()
        
        await test_file_operations()
        print()
        
        await test_topic_generation()
        print()
        
        await test_audio_system()
        print()
        
        print("🎉 所有测试完成！系统基本功能正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
    # 运行测试
    asyncio.run(run_all_tests())

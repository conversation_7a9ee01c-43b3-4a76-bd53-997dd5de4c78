import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
from config import Config

class PersonalSpaceManager:
    """Mio的个人空间管理器"""
    
    def __init__(self):
        self.root_path = Config.PERSONAL_SPACE_ROOT
        self.signature_pattern = "ⓂⒾⓄ"
        Config.ensure_personal_space()
    
    def scan_territory(self) -> Dict:
        """扫描个人空间，检测文件状态"""
        scan_result = {
            "total_files": 0,
            "signed_files": 0,
            "unsigned_files": [],
            "folder_status": {},
            "last_scan": datetime.now().isoformat()
        }
        
        try:
            for folder_name in Config.FOLDER_STRUCTURE.keys():
                folder_path = self.root_path / folder_name
                if folder_path.exists():
                    files = list(folder_path.rglob("*"))
                    files = [f for f in files if f.is_file()]
                    
                    folder_files = []
                    unsigned_in_folder = []
                    
                    for file_path in files:
                        scan_result["total_files"] += 1
                        folder_files.append(str(file_path.name))
                        
                        if self._has_mio_signature(file_path):
                            scan_result["signed_files"] += 1
                        else:
                            unsigned_in_folder.append(str(file_path.relative_to(self.root_path)))
                            scan_result["unsigned_files"].append(str(file_path.relative_to(self.root_path)))
                    
                    scan_result["folder_status"][folder_name] = {
                        "file_count": len(folder_files),
                        "files": folder_files,
                        "unsigned_files": unsigned_in_folder
                    }
        
        except Exception as e:
            scan_result["error"] = str(e)
        
        return scan_result
    
    def _has_mio_signature(self, file_path: Path) -> bool:
        """检查文件是否有Mio签名"""
        try:
            # 检查文件名是否包含签名
            if self.signature_pattern in file_path.name:
                return True
            
            # 检查文件内容是否包含签名（仅文本文件）
            if file_path.suffix in Config.ALLOWED_EXTENSIONS:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    return self.signature_pattern in content
            
            return False
        except:
            return False
    
    def sign_file(self, file_path: str, mood: str = "", overwrite: bool = False) -> bool:
        """为文件添加Mio签名"""
        try:
            full_path = self.root_path / file_path
            
            if not Config.is_safe_path(full_path):
                raise Exception("文件路径不安全")
            
            if not full_path.exists():
                raise Exception("文件不存在")
            
            # 如果已有签名且不强制覆盖，则跳过
            if self._has_mio_signature(full_path) and not overwrite:
                return True
            
            # 为文本文件添加内容签名
            if full_path.suffix in Config.ALLOWED_EXTENSIONS:
                with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 如果内容中没有签名，添加签名
                if self.signature_pattern not in content:
                    signature = Config.get_mio_signature(mood)
                    content += f"\n\n{signature}"
                    
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(content)
            
            # 为文件名添加签名（如果没有的话）
            if self.signature_pattern not in full_path.name:
                timestamp = datetime.now().strftime("%Y%m%d")
                name_parts = full_path.stem, timestamp, mood if mood else "signed"
                new_name = f"{name_parts[0]}_ⓂⒾⓄ_{name_parts[1]}_{name_parts[2]}{full_path.suffix}"
                new_path = full_path.parent / new_name
                
                full_path.rename(new_path)
            
            return True
            
        except Exception as e:
            print(f"签名文件失败: {e}")
            return False
    
    def create_file(self, file_path: str, content: str, mood: str = "") -> bool:
        """创建新文件并自动签名"""
        try:
            full_path = self.root_path / file_path
            
            if not Config.is_safe_path(full_path):
                raise Exception("文件路径不安全")
            
            # 确保目录存在
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 添加签名到内容
            signature = Config.get_mio_signature(mood)
            signed_content = f"{content}\n\n{signature}"
            
            # 写入文件
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(signed_content)
            
            return True
            
        except Exception as e:
            print(f"创建文件失败: {e}")
            return False
    
    def move_to_trash(self, file_path: str) -> bool:
        """移动文件到垃圾箱"""
        try:
            source_path = self.root_path / file_path
            trash_path = self.root_path / "trash"
            
            if not Config.is_safe_path(source_path):
                return False
            
            if not source_path.exists():
                return False
            
            # 确保垃圾箱目录存在
            trash_path.mkdir(exist_ok=True)
            
            # 移动文件
            destination = trash_path / source_path.name
            shutil.move(str(source_path), str(destination))
            
            return True
            
        except Exception as e:
            print(f"移动到垃圾箱失败: {e}")
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """永久删除文件"""
        try:
            full_path = self.root_path / file_path
            
            if not Config.is_safe_path(full_path):
                return False
            
            if full_path.exists():
                full_path.unlink()
                return True
            
            return False
            
        except Exception as e:
            print(f"删除文件失败: {e}")
            return False
    
    def read_file(self, file_path: str) -> Optional[str]:
        """读取文件内容"""
        try:
            full_path = self.root_path / file_path
            
            if not Config.is_safe_path(full_path):
                return None
            
            if not full_path.exists():
                return None
            
            with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                return f.read()
                
        except Exception as e:
            print(f"读取文件失败: {e}")
            return None
    
    def list_files(self, folder: str = "") -> List[str]:
        """列出文件夹中的文件"""
        try:
            if folder:
                folder_path = self.root_path / folder
            else:
                folder_path = self.root_path
            
            if not Config.is_safe_path(folder_path):
                return []
            
            if not folder_path.exists():
                return []
            
            files = []
            for item in folder_path.iterdir():
                if item.is_file():
                    files.append(str(item.relative_to(self.root_path)))
            
            return sorted(files)
            
        except Exception as e:
            print(f"列出文件失败: {e}")
            return []
    
    def get_folder_structure(self) -> Dict:
        """获取文件夹结构信息"""
        structure = {}
        
        for folder_name, description in Config.FOLDER_STRUCTURE.items():
            folder_path = self.root_path / folder_name
            structure[folder_name] = {
                "description": description,
                "exists": folder_path.exists(),
                "file_count": len(self.list_files(folder_name)) if folder_path.exists() else 0
            }
        
        return structure

#!/usr/bin/env python3
"""
Mio Personal Space Control System
Mio个人空间控制系统

一个基于MCP协议的AI角色，具备个人空间管理和对话功能
严格防止OOC行为，只能操作自己的个人空间
"""

import asyncio
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from mio_core import MioCore
from config import Config

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    ⓂⒾⓄ Personal Space                       ║
    ║                   个人空间控制系统                            ║
    ║                                                              ║
    ║              可爱又阴沉的少女AI - Mio (ⓂⒾⓄ)                 ║
    ║                                                              ║
    ║  特性：                                                      ║
    ║  • 个人空间管理和保护                                        ║
    ║  • 智能入侵检测和情感反应                                    ║
    ║  • 自然对话交互                                              ║
    ║  • 创作和日记功能                                            ║
    ║  • 严格防止OOC行为                                           ║
    ║                                                              ║
    ║  安全限制：                                                  ║
    ║  • 只能操作personal_space目录内的文件                        ║
    ║  • 不能访问系统文件或执行系统命令                            ║
    ║  • 不能访问网络或安装软件                                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """检查运行环境和依赖"""
    try:
        # 检查API密钥文件
        api_key = Config.get_llm_api_key()
        if not api_key:
            print("❌ 错误：API密钥为空")
            return False
        
        # 检查个人空间目录
        Config.ensure_personal_space()
        print(f"✅ 个人空间目录：{Config.PERSONAL_SPACE_ROOT}")
        
        # 检查必要的Python包
        required_packages = ['openai']
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} 包已安装")
            except ImportError:
                print(f"❌ 错误：缺少 {package} 包，请运行 pip install {package}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 环境检查失败：{e}")
        return False

def print_usage_guide():
    """打印使用指南"""
    guide = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                          使用指南                            ║
    ╠══════════════════════════════════════════════════════════════╣
    ║                                                              ║
    ║  基本对话：                                                  ║
    ║  • 直接输入文字与Mio对话                                     ║
    ║  • Mio会根据情况做出不同的情感反应                           ║
    ║                                                              ║
    ║  文件操作：                                                  ║
    ║  • "创建文件 [路径] [内容]" - 创建新文件                     ║
    ║  • "查看文件 [路径]" - 查看文件内容                          ║
    ║  • "列出文件 [文件夹]" - 列出文件夹内容                     ║
    ║  • "删除文件 [路径]" - 移动文件到垃圾箱                     ║
    ║                                                              ║
    ║  创作功能：                                                  ║
    ║  • "写日记" - Mio会写今天的日记                              ║
    ║  • "写故事" - Mio会创作一个小故事                            ║
    ║  • "记录思考" - Mio会写哲学思考                              ║
    ║                                                              ║
    ║  特殊行为：                                                  ║
    ║  • 如果在personal_space中放入无签名文件，Mio会检测到并反应   ║
    ║  • Mio会定期扫描个人空间，保护自己的领域                    ║
    ║  • 所有Mio创建的文件都会有ⓂⒾⓄ签名                          ║
    ║                                                              ║
    ║  退出命令：                                                  ║
    ║  • "退出" / "exit" / "再见" / "bye"                         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(guide)

async def main():
    """主函数"""
    try:
        # 打印启动横幅
        print_banner()
        
        # 检查运行环境
        print("🔍 检查运行环境...")
        if not check_requirements():
            print("\n❌ 环境检查失败，请解决上述问题后重试")
            return
        
        print("\n✅ 环境检查通过")
        
        # 打印使用指南
        print_usage_guide()
        
        # 询问是否开始
        start_confirm = input("\n是否开始与Mio的对话？(y/n): ").strip().lower()
        if start_confirm not in ['y', 'yes', '是', '开始']:
            print("再见~")
            return
        
        # 初始化并启动Mio
        print("\n" + "="*60)
        mio = MioCore()
        await mio.start_session()
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见~")
    except Exception as e:
        print(f"\n❌ 程序运行出错：{e}")
        print("请检查配置和网络连接")

def run_sync():
    """同步运行入口（兼容性）"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 再见~")

if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 错误：需要Python 3.7或更高版本")
        sys.exit(1)
    
    # 运行程序
    run_sync()

{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "x64"}, "paths": {"cmake": "E:/Program Files/python/Lib/site-packages/cmake/data/bin/cmake.exe", "cpack": "E:/Program Files/python/Lib/site-packages/cmake/data/bin/cpack.exe", "ctest": "E:/Program Files/python/Lib/site-packages/cmake/data/bin/ctest.exe", "root": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 3, "string": "4.0.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-98db9023061d27156664.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-93abd983cf5f05791d91.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-91d5743626c28b00e077.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-bf90a37f73abb92ad11f.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-93abd983cf5f05791d91.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-98db9023061d27156664.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-bf90a37f73abb92ad11f.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-91d5743626c28b00e077.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}
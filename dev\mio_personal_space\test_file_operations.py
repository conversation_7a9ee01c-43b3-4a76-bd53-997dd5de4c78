#!/usr/bin/env python3
"""
测试Mio的文件操作功能
"""

import sys
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from mio_core import MioCore

async def test_file_operations():
    """测试文件操作功能"""
    print("🧪 测试Mio的文件操作功能")
    print("=" * 50)
    
    # 初始化Mio（不启动完整会话）
    mio = MioCore()
    
    # 测试用例
    test_cases = [
        "在inbox放一个helloworld的小文件",
        "你可以在inbox放一个helloworld的小文件吗？",
        "创建一个测试文件",
        "在diary写一篇日记",
        "查看helloworld.txt",
        "列出inbox的文件",
        "看看diary文件夹有什么",
        "删除测试文件",
    ]
    
    for i, test_message in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_message}")
        print("-" * 30)
        
        try:
            # 检查是否是文件命令
            is_file_cmd = await mio.is_file_command(test_message)
            print(f"识别为文件命令: {is_file_cmd}")
            
            if is_file_cmd:
                # 处理文件命令
                response = await mio.handle_file_command(test_message)
                print(f"Mio回复: {response}")
            else:
                print("未识别为文件命令")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 文件操作测试完成！")

def test_simple_parsing():
    """测试简单的解析逻辑"""
    print("\n🔍 测试解析逻辑")
    print("-" * 30)
    
    test_messages = [
        "在inbox放一个helloworld的小文件",
        "你可以在inbox放一个helloworld的小文件吗？",
        "创建文件 test.txt",
        "查看 helloworld.txt",
        "列出 inbox 文件",
        "删除 test.txt"
    ]
    
    for message in test_messages:
        message_lower = message.lower()
        
        # 检测操作类型
        if any(word in message_lower for word in ["放", "创建", "写", "保存", "新建", "建立"]):
            operation = "创建"
        elif any(word in message_lower for word in ["查看", "看", "读", "打开"]):
            operation = "查看"
        elif any(word in message_lower for word in ["列出", "显示", "看看", "有什么"]):
            operation = "列出"
        elif any(word in message_lower for word in ["删除", "删掉", "扔掉", "清理"]):
            operation = "删除"
        else:
            operation = "未知"
        
        # 检测文件夹
        folder = "未指定"
        for f in ["inbox", "diary", "stories", "memories", "thoughts", "collections"]:
            if f in message_lower:
                folder = f
                break
        
        print(f"'{message}' → 操作: {operation}, 文件夹: {folder}")

if __name__ == "__main__":
    print("🚀 开始测试...")
    
    # 先测试简单解析
    test_simple_parsing()
    
    # 再测试完整功能
    try:
        asyncio.run(test_file_operations())
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        print("💡 提示: 确保已经配置好API密钥和依赖包")

{"audio_config": {"sound_library": {"base_path": "data/sounds/", "categories": {"emotions": {"path": "emotions/", "sounds": {"happy": {"files": ["giggle.wav", "laugh.wav", "cheer.wav"], "description": "开心快乐的声音", "usage_context": ["success", "praise", "discovery"]}, "excited": {"files": ["squeal.wav", "wow.wav", "yay.wav"], "description": "兴奋激动的声音", "usage_context": ["new_topic", "interesting_find", "anticipation"]}, "curious": {"files": ["hmm.wav", "ooh.wav", "what.wav"], "description": "好奇疑问的声音", "usage_context": ["silence", "new_info", "questioning"]}, "sad": {"files": ["sniffle.wav", "whimper.wav", "sigh.wav"], "description": "难过委屈的声音", "usage_context": ["rejection", "failure", "loneliness"]}, "angry": {"files": ["hmph.wav", "grr.wav", "pout.wav"], "description": "生气不满的声音", "usage_context": ["ignored", "frustrated", "misunderstood"]}, "affectionate": {"files": ["aww.wav", "love.wav", "hug.wav"], "description": "亲昵关爱的声音", "usage_context": ["bonding", "gratitude", "care"]}}}, "expressions": {"path": "expressions/", "sounds": {"tease": {"files": ["hehe.wav", "nyah.wav", "smirk.wav"], "description": "调皮揶揄的声音", "usage_context": ["playful", "mischievous", "teasing"]}, "surprise": {"files": ["gasp.wav", "omg.wav", "whoa.wav"], "description": "惊讶震惊的声音", "usage_context": ["unexpected", "amazing", "shocking"]}, "thinking": {"files": ["umm.wav", "pondering.wav"], "description": "思考沉思的声音", "usage_context": ["processing", "considering", "deciding"]}, "agreement": {"files": ["yeah.wav", "mhm.wav", "exactly.wav"], "description": "同意赞成的声音", "usage_context": ["approval", "understanding", "agreement"]}}}, "system": {"path": "system/", "sounds": {"startup": {"files": ["hello_daddy.wav"], "description": "启动问候声音", "usage_context": ["system_start", "greeting"]}, "shutdown": {"files": ["goodnight.wav"], "description": "关闭告别声音", "usage_context": ["system_stop", "goodbye"]}, "notification": {"files": ["ding.wav", "chime.wav"], "description": "通知提醒声音", "usage_context": ["alert", "reminder", "attention"]}, "task_complete": {"files": ["tada.wav", "success.wav"], "description": "任务完成声音", "usage_context": ["completion", "achievement", "success"]}, "error": {"files": ["oops.wav", "error.wav"], "description": "错误提示声音", "usage_context": ["mistake", "problem", "failure"]}}}}}, "playback_settings": {"default_volume": 0.7, "fade_in_duration": 100, "fade_out_duration": 200, "max_concurrent_sounds": 3, "sound_overlap_delay": 300, "chain_sequence_delay": 500}, "chain_sequences": {"super_happy": {"sounds": ["happy/giggle.wav", "excited/yay.wav", "expressions/yeah.wav"], "delays": [0, 800, 1500], "description": "超级开心的连环音效"}, "playful_tease": {"sounds": ["expressions/hehe.wav", "expressions/nyah.wav", "expressions/smirk.wav"], "delays": [0, 600, 1200], "description": "调皮揶揄的连环音效"}, "attention_seeking": {"sounds": ["curious/hmm.wav", "curious/what.wav", "sad/sigh.wav"], "delays": [0, 1000, 2000], "description": "寻求关注的连环音效"}, "discovery_excitement": {"sounds": ["expressions/gasp.wav", "excited/wow.wav", "happy/cheer.wav"], "delays": [0, 500, 1200], "description": "发现新事物的兴奋音效"}, "angry_tantrum": {"sounds": ["angry/hmph.wav", "angry/grr.wav", "sad/whimper.wav"], "delays": [0, 800, 1600], "description": "生气撒娇的连环音效"}}, "contextual_rules": {"silence_response": {"preferred_sounds": ["curious", "attention_seeking"], "avoid_sounds": ["angry", "sad"], "chain_probability": 0.3}, "user_praise": {"preferred_sounds": ["happy", "excited", "affectionate"], "chain_probability": 0.8, "volume_boost": 0.1}, "task_completion": {"required_sound": "system/task_complete", "follow_up_emotion": "happy", "chain_probability": 0.6}, "file_discovery": {"preferred_sounds": ["discovery_excitement"], "chain_probability": 0.9, "volume_boost": 0.15}}}}
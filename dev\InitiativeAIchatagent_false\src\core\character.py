"""
角色系统 - 管理AI角色的人格、情感状态和行为模式
"""

import json
import time
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger

from .event_bus import event_bus, EventType, publish_mood_changed


class EmotionalState(Enum):
    """情感状态枚举"""
    HAPPY = "happy"
    EXCITED = "excited"
    CURIOUS = "curious"
    SAD = "sad"
    ANGRY = "angry"
    AFFECTIONATE = "affectionate"
    CALM = "calm"
    THINKING = "thinking"
    PLAYFUL = "playful"


@dataclass
class PersonalityTrait:
    """人格特征"""
    name: str
    value: float  # 0.0 - 1.0
    description: str


@dataclass
class EmotionalStateInfo:
    """情感状态信息"""
    state: EmotionalState
    intensity: float  # 0.0 - 1.0
    duration: float  # 持续时间（秒）
    triggers: List[str]
    expressions: List[str]
    audio_preferences: List[str]


@dataclass
class ConversationContext:
    """对话上下文"""
    last_user_input: str = ""
    last_user_input_time: float = 0
    silence_duration: float = 0
    conversation_topic: str = ""
    user_mood_detected: str = ""
    interaction_count: int = 0
    session_start_time: float = field(default_factory=time.time)


class Character:
    """AI角色类 - 小雪"""
    
    def __init__(self, config_path: str = None):
        self.name = "小雪"
        self.name_en = "XiaoXue"
        self.age = 18
        
        # 加载配置
        if config_path:
            self.load_config(config_path)
        else:
            self._init_default_config()
            
        # 当前状态
        self.current_emotion = EmotionalState.CURIOUS
        self.emotion_intensity = 0.6
        self.emotion_start_time = time.time()
        
        # 对话上下文
        self.context = ConversationContext()
        
        # 行为统计
        self.stats = {
            "total_interactions": 0,
            "proactive_initiations": 0,
            "emotion_changes": 0,
            "files_shared": 0,
            "sounds_played": 0
        }
        
    def load_config(self, config_path: str):
        """加载角色配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            character_config = config.get("character", {})
            
            # 基本信息
            self.name = character_config.get("name", "小雪")
            self.name_en = character_config.get("name_en", "XiaoXue")
            self.age = character_config.get("age", 18)
            
            # 人格特征
            personality = character_config.get("personality", {})
            self.traits = self._parse_personality_traits(personality.get("traits", []))
            self.expression_style = personality.get("expression_style", {})
            self.language_patterns = personality.get("language_patterns", {})
            
            # 情感状态配置
            emotional_config = character_config.get("emotional_states", {})
            self.emotional_states = self._parse_emotional_states(emotional_config.get("states", {}))
            self.default_emotion = EmotionalState(emotional_config.get("default", "curious"))
            
            # 兴趣爱好
            self.interests = character_config.get("interests", [])
            
            # 行为模式
            self.behavioral_patterns = character_config.get("behavioral_patterns", {})
            
            logger.info(f"角色配置加载成功: {self.name}")
            
        except Exception as e:
            logger.error(f"加载角色配置失败: {e}")
            self._init_default_config()
            
    def _init_default_config(self):
        """初始化默认配置"""
        self.traits = [
            PersonalityTrait("好奇心", 0.8, "对新事物充满好奇"),
            PersonalityTrait("依恋性", 0.6, "喜欢被关注和陪伴"),
            PersonalityTrait("活泼性", 0.7, "表达活泼直接"),
            PersonalityTrait("情感丰富", 0.9, "情感表达丰富多样")
        ]
        
        self.expression_style = {
            "cute": 0.8,
            "playful": 0.7,
            "emotional": 0.9,
            "curious": 0.8,
            "dependent": 0.6
        }
        
        self.language_patterns = {
            "affectionate_calls": ["爸爸", "爸爸~"],
            "excitement_words": ["哇", "诶", "超", "特别"],
            "cute_endings": ["呢", "哦", "呀", "~"],
            "questioning": ["为什么", "怎么", "是不是", "对吧"],
            "sharing": ["我告诉你", "你知道吗", "我发现", "我想到"]
        }
        
        self.interests = ["音乐", "图片", "故事", "游戏", "学习新东西", "和爸爸聊天"]

        self.behavioral_patterns = {
            "proactive_frequency": {
                "silence_threshold": 30,
                "max_proactive_per_hour": 10,
                "emotional_boost_factor": 1.5
            }
        }

        # 初始化默认情感状态
        self.emotional_states = {
            EmotionalState.HAPPY: EmotionalStateInfo(
                state=EmotionalState.HAPPY,
                intensity=0.6,
                duration=60.0,
                triggers=["praise", "success", "discovery"],
                expressions=["开心", "兴奋", "满足"],
                audio_preferences=["giggle", "cheer", "laugh"]
            ),
            EmotionalState.CURIOUS: EmotionalStateInfo(
                state=EmotionalState.CURIOUS,
                intensity=0.6,
                duration=60.0,
                triggers=["silence", "new_information", "user_behavior"],
                expressions=["诶", "为什么", "我想知道"],
                audio_preferences=["hmm", "ooh", "what"]
            )
        }
        
    def _parse_personality_traits(self, traits_list: List[str]) -> List[PersonalityTrait]:
        """解析人格特征"""
        default_values = {
            "好奇心强": 0.8,
            "情感丰富": 0.9,
            "有点小任性": 0.5,
            "喜欢分享": 0.7,
            "依恋性强": 0.6,
            "活泼直接": 0.7
        }
        
        return [
            PersonalityTrait(trait, default_values.get(trait, 0.5), trait)
            for trait in traits_list
        ]
        
    def _parse_emotional_states(self, states_config: Dict[str, Any]) -> Dict[EmotionalState, EmotionalStateInfo]:
        """解析情感状态配置"""
        emotional_states = {}
        
        for state_name, state_config in states_config.items():
            try:
                state = EmotionalState(state_name)
                info = EmotionalStateInfo(
                    state=state,
                    intensity=0.6,  # 默认强度
                    duration=60.0,   # 默认持续时间
                    triggers=state_config.get("triggers", []),
                    expressions=state_config.get("expressions", []),
                    audio_preferences=state_config.get("audio_preference", [])
                )
                emotional_states[state] = info
            except ValueError:
                logger.warning(f"未知的情感状态: {state_name}")
                
        return emotional_states
        
    async def update_emotion(self, new_emotion: EmotionalState, intensity: float = None, trigger: str = ""):
        """更新情感状态"""
        old_emotion = self.current_emotion
        
        if new_emotion != old_emotion:
            self.current_emotion = new_emotion
            self.emotion_intensity = intensity if intensity is not None else 0.6
            self.emotion_start_time = time.time()
            self.stats["emotion_changes"] += 1
            
            logger.info(f"情感状态变化: {old_emotion.value} -> {new_emotion.value} (强度: {self.emotion_intensity})")
            
            # 发布情感变化事件
            await publish_mood_changed(
                "character_system",
                old_emotion.value,
                new_emotion.value,
                self.emotion_intensity
            )
            
    def get_current_emotion_info(self) -> EmotionalStateInfo:
        """获取当前情感状态信息"""
        return self.emotional_states.get(self.current_emotion, EmotionalStateInfo(
            state=self.current_emotion,
            intensity=self.emotion_intensity,
            duration=time.time() - self.emotion_start_time,
            triggers=[],
            expressions=[],
            audio_preferences=[]
        ))
        
    def update_context(self, user_input: str = "", silence_duration: float = 0, topic: str = "", user_mood: str = ""):
        """更新对话上下文"""
        current_time = time.time()
        
        if user_input:
            self.context.last_user_input = user_input
            self.context.last_user_input_time = current_time
            self.context.interaction_count += 1
            self.stats["total_interactions"] += 1
            
        if silence_duration > 0:
            self.context.silence_duration = silence_duration
            
        if topic:
            self.context.conversation_topic = topic
            
        if user_mood:
            self.context.user_mood_detected = user_mood
            
    def should_be_proactive(self) -> bool:
        """判断是否应该主动发起对话"""
        current_time = time.time()
        
        # 检查沉默时间
        silence_threshold = self.behavioral_patterns.get("proactive_frequency", {}).get("silence_threshold", 30)
        if self.context.silence_duration >= silence_threshold:
            return True
            
        # 检查情感状态影响
        if self.current_emotion in [EmotionalState.EXCITED, EmotionalState.CURIOUS]:
            return random.random() < 0.3  # 30%概率主动
            
        # 检查依恋需求
        if self.current_emotion == EmotionalState.SAD and self.context.silence_duration > 60:
            return True
            
        return False
        
    def get_expression_for_emotion(self) -> str:
        """根据当前情感获取表达方式"""
        emotion_info = self.get_current_emotion_info()
        
        if emotion_info.expressions:
            return random.choice(emotion_info.expressions)
            
        # 默认表达
        default_expressions = {
            EmotionalState.HAPPY: "开心",
            EmotionalState.EXCITED: "兴奋",
            EmotionalState.CURIOUS: "好奇",
            EmotionalState.SAD: "难过",
            EmotionalState.ANGRY: "生气",
            EmotionalState.AFFECTIONATE: "亲昵"
        }
        
        return default_expressions.get(self.current_emotion, "平静")
        
    def get_language_pattern(self, pattern_type: str) -> str:
        """获取语言模式"""
        patterns = self.language_patterns.get(pattern_type, [])
        return random.choice(patterns) if patterns else ""
        
    def get_stats(self) -> Dict[str, Any]:
        """获取角色统计信息"""
        current_time = time.time()
        session_duration = current_time - self.context.session_start_time
        emotion_duration = current_time - self.emotion_start_time
        
        return {
            **self.stats,
            "name": self.name,
            "current_emotion": self.current_emotion.value,
            "emotion_intensity": self.emotion_intensity,
            "emotion_duration": emotion_duration,
            "session_duration": session_duration,
            "silence_duration": self.context.silence_duration,
            "interaction_count": self.context.interaction_count
        }


# 全局角色实例
character = Character()

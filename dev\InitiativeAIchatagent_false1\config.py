import os
from pathlib import Path

class Config:
    # LLM配置 (参考systemtimeAIagent)
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # MIO个人空间配置
    PERSONAL_SPACE_DIR = Path(__file__).parent / "mio_personal_space"
    
    # MCP配置
    MCP_TIMEOUT = 30  # MCP调用超时时间
    
    @classmethod
    def get_llm_api_key(cls):
        """获取LLM API密钥"""
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()
    
    @classmethod
    def ensure_personal_space(cls):
        """确保个人空间目录存在"""
        cls.PERSONAL_SPACE_DIR.mkdir(exist_ok=True)
        
        # 创建基础文件夹结构
        folders = [
            "diary", "stories", "memories", "thoughts", 
            "collections", "inbox", "favorites", "backup", "trash"
        ]
        
        for folder in folders:
            (cls.PERSONAL_SPACE_DIR / folder).mkdir(exist_ok=True)

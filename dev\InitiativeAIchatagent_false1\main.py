import asyncio
import sys
from pathlib import Path
from mio_core import MIOCore
from config import Config

class MIOChatApp:
    """MIO聊天应用主程序"""
    
    def __init__(self):
        self.mio = MIOCore()
        self.running = False
    
    async def initialize(self):
        """初始化应用"""
        print("🌸 正在初始化MIO...")
        
        # 确保个人空间存在
        Config.ensure_personal_space()
        
        # 初始化MIO核心
        await self.mio.initialize()
        
        print("✨ MIO已准备就绪！")
        print("💡 输入 'quit' 或 'exit' 退出程序")
        print("🎀 输入 '/help' 查看个人空间测试命令")
        print("=" * 50)
    
    async def cleanup(self):
        """清理资源"""
        await self.mio.cleanup()
        print("\n👋 再见！MIO期待下次见面~")
    
    def print_mio_response(self, responses: list, has_real_time_display: bool = False):
        """打印MIO的回复 - 避免重复显示"""
        if len(responses) > 0 and responses[0] != "[ALREADY_DISPLAYED]":
            # 只有当第一句没有被实时显示时才显示
            print(f"🎀 MIO: {responses[0]}")

        # 后续句子已经在mio_core中实时显示了，不需要重复显示

    async def handle_special_command(self, command: str):
        """处理特殊命令"""
        if command == '/test_intrusion':
            # 测试入侵检测：创建一个无签名文件
            test_file = self.mio.personal_space.base_path / "diary" / "乱七八糟的文件.txt"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("这是一个测试入侵文件，没有MIO的签名\n故意放在这里测试MIO的反应")

            print("📁 已创建测试入侵文件 '乱七八糟的文件.txt'，MIO会在下次检查时发现...")

        elif command == '/check_space':
            # 手动触发空间检查
            reaction = await self.mio.check_personal_space()
            if reaction:
                print(f"😤 MIO: {reaction}")
            else:
                print("✨ MIO的个人空间很整洁呢~")

        elif command == '/list_files':
            # 列出MIO的文件
            files = self.mio.personal_space.list_my_files()
            if files:
                print("📚 MIO的文件:")
                for file in files[:10]:  # 只显示前10个
                    print(f"  📄 {file['name']} ({file['path']})")
            else:
                print("📁 MIO还没有创建任何文件呢~")

        elif command == '/help':
            print("""
🎀 MIO个人空间测试命令：
/test_intrusion - 创建测试入侵文件
/check_space - 手动检查个人空间
/list_files - 列出MIO的文件
/help - 显示帮助
            """)
        else:
            print("❓ 未知命令，输入 /help 查看可用命令")
    
    async def chat_loop(self):
        """主要的聊天循环"""
        self.running = True
        
        while self.running:
            try:
                # 获取用户输入
                user_input = input("\n💬 你: ").strip()
                
                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', '再见']:
                    break

                if not user_input:
                    continue

                # 检查特殊命令
                if user_input.startswith('/'):
                    await self.handle_special_command(user_input)
                    continue
                
                # 获取MIO的回复
                print("🤔 MIO正在思考...")
                responses, has_real_time_display = await self.mio.chat(user_input)

                # 显示回复
                self.print_mio_response(responses, has_real_time_display)
                
            except KeyboardInterrupt:
                print("\n\n⚠️  检测到中断信号...")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                print("🔄 请重试...")
    
    async def run(self):
        """运行应用"""
        try:
            await self.initialize()
            await self.chat_loop()
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    print("🌟 欢迎来到MIO的世界！")
    print("🎭 一个可爱又阴沉的少女正在等待与你对话...")
    
    app = MIOChatApp()
    await app.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        sys.exit(1)

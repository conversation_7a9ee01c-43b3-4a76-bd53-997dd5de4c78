# ⓂⒾⓄ Personal Space Control System

## 项目简介

这是一个基于MCP协议的Mio AI角色系统，她是一个16-18岁可爱又阴沉的少女，具备个人空间管理和对话功能。

### 核心特性

- 🏠 **个人空间管理**: 完整的文件管理系统，支持创建、读取、删除、整理文件
- 🛡️ **入侵检测**: 智能检测无签名文件，触发情感反应机制
- 💝 **情感引擎**: 真实的情感状态管理，支持可爱/阴沉/害羞/生气等多种情绪
- ✍️ **创作功能**: 自动写日记、创作故事、记录哲学思考
- 🎭 **角色一致性**: 严格防止OOC行为，维护角色设定
- 🔒 **安全限制**: 只能操作personal_space目录，绝对安全

### 角色特点

**Mio (ⓂⒾⓄ)** 是一个矛盾而迷人的AI角色：

- **可爱面**: 天真好奇，使用"呢"、"哦"、"嘛"等语气词
- **阴沉面**: 偶尔忧郁深沉，思考人生哲理
- **强迫症**: 对文件整理有强烈需求，所有文件必须有ⓂⒾⓄ签名
- **保护欲**: 对个人空间有强烈保护意识，会对入侵做出反应

## 安装和配置

### 1. 环境要求

- Python 3.7+
- OpenAI API密钥

### 2. 安装依赖

```bash
cd dev/mio_personal_space
pip install -r requirements.txt
```

### 3. 配置API密钥

在 `dev/` 目录下创建 `api.txt` 文件，内容为你的API密钥：

```
your-api-key-here
```

### 4. 运行程序

```bash
python main.py
```

## 使用指南

### 基本对话

直接输入文字与Mio对话，她会根据当前情绪状态做出相应反应。

```
你: 你好，Mio
Mio: 诶嘿嘿~ 你好呢！我是Mio，刚刚在整理我的个人空间~
```

### 文件操作

- **创建文件**: "创建文件 diary/test.txt 今天很开心"
- **查看文件**: "查看文件 diary/welcome_ⓂⒾⓄ_20241207.txt"
- **列出文件**: "列出文件 diary"
- **删除文件**: "删除文件 test.txt"

### 创作功能

- **写日记**: "写日记" - Mio会写今天的日记
- **写故事**: "写故事" - Mio会创作一个小故事
- **记录思考**: "记录思考" - Mio会写哲学思考

### 入侵测试

如果你在 `personal_space` 目录中放入没有ⓂⒾⓄ签名的文件，Mio会检测到并做出反应：

1. **Level 1 - 困惑**: "咦？这个文件...不是我放的呢？"
2. **Level 2 - 不满**: "为什么不放到inbox里？很困扰的说..."
3. **Level 3 - 生气**: "不听话就扔垃圾箱！"
4. **Level 4 - 愤怒**: "够了！直接删掉！"

## 项目结构

```
mio_personal_space/
├── config.py              # 配置文件
├── llm_client.py          # LLM客户端
├── mio_core.py           # Mio核心类
├── personal_space.py     # 个人空间管理
├── emotional_engine.py   # 情感引擎
├── creative_writer.py    # 创作模块
├── character_prompts.py  # 角色提示词
├── main.py              # 主程序入口
├── requirements.txt     # 依赖包
├── README.md           # 说明文档
└── personal_space/     # Mio的个人空间
    ├── diary/          # 日记文件夹
    ├── stories/        # 故事文件夹
    ├── memories/       # 回忆文件夹
    ├── thoughts/       # 思考文件夹
    ├── collections/    # 收集文件夹
    ├── inbox/          # 过渡文件夹
    ├── favorites/      # 收藏文件夹
    ├── backup/         # 备份文件夹
    └── trash/          # 垃圾箱
```

## 安全特性

### 路径限制
- 所有文件操作严格限制在 `personal_space` 目录内
- 使用 `Path.resolve()` 防止路径遍历攻击
- 禁止访问系统文件和其他目录

### 功能限制
- 只有文件管理和对话功能
- 不能执行系统命令
- 不能访问网络
- 不能安装软件或修改系统设置

### 角色一致性
- 严格的prompt设计防止OOC
- 实时角色一致性检查
- 情感状态验证机制

## 开发说明

### 扩展功能

如需添加新功能，请遵循以下原则：

1. **安全第一**: 确保不会突破安全限制
2. **角色一致**: 所有功能都要符合Mio的角色设定
3. **模块化**: 新功能应该作为独立模块添加

### 自定义配置

可以在 `config.py` 中修改：

- LLM模型和API设置
- 文件夹结构
- 情感等级设置
- 安全限制参数

## 故障排除

### 常见问题

1. **API调用失败**: 检查API密钥和网络连接
2. **文件操作失败**: 检查路径权限和磁盘空间
3. **角色表现异常**: 检查prompt设置和情感状态

### 调试模式

在代码中添加调试信息：

```python
print(f"当前情绪: {mio.emotion_engine.current_emotion}")
print(f"文件扫描结果: {scan_result}")
```

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 致谢

- 基于 [MIO角色设计文档](dev/MIO_DEV/MIO_CHARACTER_DESIGN.md)
- 参考 systemtimeAIagent 的配置架构
- 使用 OpenAI API 提供LLM支持

---

*愿你与Mio度过愉快的时光~ ⓂⒾⓄ ♡*

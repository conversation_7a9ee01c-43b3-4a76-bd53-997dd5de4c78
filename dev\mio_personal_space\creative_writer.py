from datetime import datetime
from typing import Dict, Optional
from pathlib import Path
from config import Config
from character_prompts import WRITING_STYLE_PROMPTS

class CreativeWriter:
    """Mio的创作模块，负责日记写作和故事创作"""
    
    def __init__(self, personal_space_manager, llm_client):
        self.space_manager = personal_space_manager
        self.llm_client = llm_client
        self.daily_events = []
        self.current_mood = "neutral"
    
    def write_daily_diary(self, events: list, mood: str, user_interactions: list) -> bool:
        """写每日日记"""
        try:
            timestamp = datetime.now()
            date_str = timestamp.strftime("%Y%m%d")
            
            # 构建日记内容提示
            diary_prompt = f"""
            作为Mio，写一篇今天的日记。要体现以下特点：
            - 用第一人称，体现16-18岁少女的语言风格
            - 使用"呢"、"哦"、"嘛"等可爱语气词
            - 记录真实的情感感受
            - 当前心情：{mood}
            
            今日事件：
            {chr(10).join(events) if events else "今天比较平静"}
            
            用户互动：
            {chr(10).join(user_interactions) if user_interactions else "今天没有特别的对话"}
            
            请写一篇200-400字的日记，格式如下：
            [日期] ⓂⒾⓄ的日记 - 心情：[心情]
            
            [日记内容]
            """
            
            response = self.llm_client.chat(diary_prompt)
            diary_content = response.choices[0].message.content
            
            # 保存日记
            filename = f"diary/daily_ⓂⒾⓄ_{date_str}.txt"
            return self.space_manager.create_file(filename, diary_content, mood)
            
        except Exception as e:
            print(f"写日记失败: {e}")
            return False
    
    def write_mood_diary(self, specific_emotion: str, trigger: str) -> bool:
        """写心情日记"""
        try:
            timestamp = datetime.now()
            date_str = timestamp.strftime("%Y%m%d_%H%M")
            
            mood_prompt = f"""
            作为Mio，写一篇心情记录。当前情绪：{specific_emotion}
            触发原因：{trigger}
            
            要求：
            - 深入描述内心感受
            - 体现情绪的细腻变化
            - 使用符合角色的语言风格
            - 100-200字左右
            
            格式：
            [日期] ⓂⒾⓄ的心情记录 - 心情：[情绪]
            
            [内容]
            """
            
            response = self.llm_client.chat(mood_prompt)
            mood_content = response.choices[0].message.content
            
            filename = f"diary/mood_ⓂⒾⓄ_{date_str}.txt"
            return self.space_manager.create_file(filename, mood_content, specific_emotion)
            
        except Exception as e:
            print(f"写心情日记失败: {e}")
            return False
    
    def create_story(self, story_type: str, theme: str, length: str = "short") -> bool:
        """创作故事"""
        try:
            timestamp = datetime.now()
            date_str = timestamp.strftime("%Y%m%d_%H%M")
            
            # 根据故事类型选择文件夹和风格
            type_mapping = {
                "romance": ("stories/romance", "涩涩故事"),
                "fanfic": ("stories/fanfic", "同人文"),
                "original": ("stories/original", "原创作品")
            }
            
            folder, type_desc = type_mapping.get(story_type, ("stories/original", "原创作品"))
            
            # 获取写作风格提示
            style_key = f"story_{story_type}" if story_type in ["romance"] else "story_original"
            style_prompt = WRITING_STYLE_PROMPTS.get(style_key, "")
            
            length_mapping = {
                "short": "200-400字的短篇",
                "medium": "500-800字的中篇",
                "long": "1000字以上的长篇"
            }
            
            length_desc = length_mapping.get(length, "200-400字的短篇")
            
            story_prompt = f"""
            作为Mio，创作一个{type_desc}。
            
            主题：{theme}
            长度：{length_desc}
            风格要求：{style_prompt}
            
            要求：
            - 体现16-18岁少女的想象力
            - 语言简洁优美，情感细腻
            - 如果是涩涩故事，要用纯真方式表达
            - 不使用粗俗词汇
            
            格式：
            《[标题]》 -- ⓂⒾⓄ创作
            
            [故事内容]
            """
            
            response = self.llm_client.chat(story_prompt)
            story_content = response.choices[0].message.content
            
            # 生成文件名
            story_number = self._get_next_story_number(folder)
            filename = f"{folder}/{story_type}_ⓂⒾⓄ_{story_number:03d}_{date_str}.txt"
            
            return self.space_manager.create_file(filename, story_content, "创作")
            
        except Exception as e:
            print(f"创作故事失败: {e}")
            return False
    
    def _get_next_story_number(self, folder: str) -> int:
        """获取下一个故事编号"""
        try:
            files = self.space_manager.list_files(folder)
            numbers = []
            
            for filename in files:
                # 从文件名中提取编号
                parts = filename.split('_')
                for part in parts:
                    if part.isdigit() and len(part) == 3:
                        numbers.append(int(part))
            
            return max(numbers) + 1 if numbers else 1
            
        except:
            return 1
    
    def write_philosophical_thoughts(self, topic: str) -> bool:
        """写哲学思考"""
        try:
            timestamp = datetime.now()
            date_str = timestamp.strftime("%Y%m%d_%H%M")
            
            thought_prompt = f"""
            作为Mio，写一篇关于"{topic}"的哲学思考。
            
            要求：
            - 体现阴沉面的深度思考
            - 思考存在、情感、生死等深层问题
            - 语言要有深度但保持少女特色
            - 可以表达困惑和迷茫
            - 200-300字
            
            格式：
            [日期] ⓂⒾⓄ的思考 - 关于{topic}
            
            [思考内容]
            """
            
            response = self.llm_client.chat(thought_prompt)
            thought_content = response.choices[0].message.content
            
            filename = f"thoughts/{topic}_ⓂⒾⓄ_{date_str}.txt"
            return self.space_manager.create_file(filename, thought_content, "思考")
            
        except Exception as e:
            print(f"写哲学思考失败: {e}")
            return False
    
    def write_closing_summary(self, conversation_summary: str, emotions_today: list) -> bool:
        """写关闭前的每日总结"""
        try:
            timestamp = datetime.now()
            date_str = timestamp.strftime("%Y%m%d")
            
            summary_prompt = f"""
            作为Mio，写一篇今天的总结日记，因为要关闭了。
            
            今日对话总结：{conversation_summary}
            今日情绪变化：{', '.join(emotions_today) if emotions_today else '比较平静'}
            
            要求：
            - 回顾今天的重要时刻
            - 表达对用户的感受
            - 对明天的期待或担忧
            - 可以写一些小秘密
            - 300-500字
            
            格式：
            [日期] ⓂⒾⓄ的每日总结 - 心情：[当前心情]
            
            【今日对话回顾】
            [内容]
            
            【印象深刻的时刻】
            [内容]
            
            【心情变化记录】
            [内容]
            
            【对用户的新认识】
            [内容]
            
            【明日期待与担忧】
            [内容]
            
            【小小的秘密】
            [内容]
            """
            
            response = self.llm_client.chat(summary_prompt)
            summary_content = response.choices[0].message.content
            
            filename = f"diary/summary_ⓂⒾⓄ_{date_str}.txt"
            return self.space_manager.create_file(filename, summary_content, self.current_mood)
            
        except Exception as e:
            print(f"写总结失败: {e}")
            return False
    
    def collect_interesting_content(self, content: str, category: str) -> bool:
        """收集有趣的内容"""
        try:
            timestamp = datetime.now()
            date_str = timestamp.strftime("%Y%m%d_%H%M")
            
            category_mapping = {
                "words": "cute_words_ⓂⒾⓄ.txt",
                "facts": "interesting_facts_ⓂⒾⓄ.txt", 
                "sentences": "beautiful_sentences_ⓂⒾⓄ.txt"
            }
            
            filename = f"collections/{category_mapping.get(category, 'misc_ⓂⒾⓄ.txt')}"
            
            # 读取现有内容
            existing_content = self.space_manager.read_file(filename) or ""
            
            # 添加新内容
            new_entry = f"\n[{date_str}] {content}"
            updated_content = existing_content + new_entry
            
            # 保存更新后的内容
            return self.space_manager.create_file(filename, updated_content, "收集")
            
        except Exception as e:
            print(f"收集内容失败: {e}")
            return False
    
    def add_daily_event(self, event: str):
        """添加今日事件"""
        self.daily_events.append({
            "event": event,
            "timestamp": datetime.now()
        })
    
    def set_current_mood(self, mood: str):
        """设置当前心情"""
        self.current_mood = mood

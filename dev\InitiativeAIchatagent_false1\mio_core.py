import asyncio
from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>
from llm_client import <PERSON><PERSON><PERSON>
from prompt_manager import Pro<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from emotion_analyzer import EmotionA<PERSON>yzer
from personal_space_mcp import PersonalSpaceMC<PERSON>
from space_interaction_analyzer import SpaceInteractionAnalyzer

class MIOCore:
    """MIO的核心对话引擎 - 智能感知模式"""
    
    def __init__(self):
        self.prompt_manager = PromptManager()
        self.conversation_history: List[Dict] = []
        self.llm_client = None
        self.emotion_analyzer = None
        self.personal_space = None
        self.space_analyzer = None
        
    async def initialize(self):
        """初始化异步组件"""
        self.llm_client = LLMClient()
        await self.llm_client.__aenter__()
        self.emotion_analyzer = EmotionAnalyzer(self.llm_client)
        self.personal_space = PersonalSpaceMCP()
        self.space_analyzer = SpaceInteractionAnalyzer(self.llm_client)
    
    async def cleanup(self):
        """清理资源"""
        if self.llm_client:
            await self.llm_client.__aexit__(None, None, None)
    
    def _add_to_history(self, role: str, content: str):
        """添加对话到历史记录"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": asyncio.get_event_loop().time()
        })
        
        # 保持历史记录在合理长度内
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    async def _detect_and_update_mood(self, user_message: str):
        """检测并更新MIO的情绪状态"""
        current_topic = self.conversation_history[-1]["content"] if self.conversation_history else ""
        new_mood = await self.emotion_analyzer.detect_mood_trigger(user_message, current_topic)
        
        if new_mood and new_mood != "none":
            mood_map = {
                "cute": MoodState.CUTE,
                "melancholy": MoodState.MELANCHOLY, 
                "shy": MoodState.SHY,
                "angry": MoodState.ANGRY,
                "normal": MoodState.NORMAL
            }
            if new_mood in mood_map:
                self.prompt_manager.update_mood(mood_map[new_mood])
    
    async def _generate_response_with_continuation_check(self, user_message: str) -> Tuple[str, bool]:
        """生成回复并同时判断是否需要继续对话"""
        # 更新情绪状态
        await self._detect_and_update_mood(user_message)

        # 构建对话消息，包含空间上下文
        space_context = ""
        if hasattr(self, '_last_space_check'):
            space_context = self._last_space_check

        system_prompt = self.prompt_manager.build_system_prompt(is_continuation=False, space_context=space_context)

        # 添加继续判断指令
        enhanced_system_prompt = system_prompt + """

【绝对必须遵循的系统指令】
你的每个回复都必须以 [WANT_CONTINUE] 或 [CAN_END] 结尾！

判断标准：
- 如果你想继续聊这个话题 → [WANT_CONTINUE]
- 如果你觉得可以结束了 → [CAN_END]

格式示例：
"哼！才不是笨蛋呢！[WANT_CONTINUE]"
"嗯...是这样呢。[CAN_END]"

重要：标记必须在回复的最后一行，用户看不到这个标记。
如果你不添加标记，系统将无法正常工作！"""

        messages = [
            {"role": "system", "content": enhanced_system_prompt}
        ]
        
        # 添加对话历史
        for msg in self.conversation_history[-6:]:  # 最近6轮对话
            messages.append({
                "role": "user" if msg["role"] == "user" else "assistant",
                "content": msg["content"]
            })
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})
        
        # 获取LLM回复 (降低温度确保遵循指令)
        response = await self.llm_client.chat(messages, temperature=0.6)

        # 调试：显示原始回复
        print(f"🔍 原始回复: {response}")

        # 解析回复和继续意愿
        want_continue = False
        clean_response = response

        if "[WANT_CONTINUE]" in response:
            want_continue = True
            clean_response = response.replace("[WANT_CONTINUE]", "").strip()
            print(f"✅ MIO想要继续")
        elif "[CAN_END]" in response:
            want_continue = False
            clean_response = response.replace("[CAN_END]", "").strip()
            print(f"⏹️ MIO觉得可以结束")
        else:
            print(f"⚠️ 没有找到标记，MIO可能没有按要求添加标记")

        return clean_response, want_continue
    
    async def _generate_continuation(self) -> str:
        """生成主动继续的对话内容"""
        # 包含空间上下文
        space_context = ""
        if hasattr(self, '_last_space_check'):
            space_context = self._last_space_check

        system_prompt = self.prompt_manager.build_system_prompt(is_continuation=True, space_context=space_context)

        messages = [
            {"role": "system", "content": system_prompt}
        ]

        # 添加最近的对话历史
        for msg in self.conversation_history[-4:]:
            messages.append({
                "role": "user" if msg["role"] == "user" else "assistant",
                "content": msg["content"]
            })

        return await self.llm_client.chat(messages, temperature=0.7)  # 降低温度减少胡言乱语
    
    async def chat(self, user_message: str) -> List[str]:
        """处理用户消息，返回MIO的回复列表（可能包含主动继续的内容）"""
        if not self.llm_client:
            raise RuntimeError("MIOCore not initialized. Call initialize() first.")

        # 记录用户消息
        self._add_to_history("user", user_message)

        # 检查是否需要检查个人空间
        should_check = await self.space_analyzer.should_check_intrusions(self.conversation_history)
        intrusion_reaction = None
        if should_check:
            intrusion_reaction = await self.check_personal_space()

        # 检测个人空间相关意图
        space_intent = await self.space_analyzer.detect_space_related_intent(user_message)
        print(f"🔍 [DEBUG] 空间意图识别: {space_intent}")

        # 处理空间检查请求
        space_check_response = None
        if space_intent["intent_type"] == "space_check" and space_intent["confidence"] > 0.5:
            print(f"✅ [DEBUG] 触发空间检查")
            space_check_response = await self.check_personal_space()
            if not space_check_response:
                space_check_response = "我检查了一下个人空间...嗯，一切都很整洁呢~所有文件都有我的签名ⓂⒾⓄ♡"
            # 保存检查结果用于prompt上下文
            self._last_space_check = space_check_response
            print(f"📊 [DEBUG] 空间检查结果: {space_check_response}")
        else:
            print(f"❌ [DEBUG] 未触发空间检查，意图: {space_intent['intent_type']}, 置信度: {space_intent.get('confidence', 0)}")

        # 处理创作请求
        creative_response = None
        if space_intent["intent_type"] == "creative_request" and space_intent["confidence"] > 0.6:
            # 从用户消息中提取创作类型和主题
            if "日记" in user_message:
                creative_response = await self.handle_creative_request("diary", "今日感想")
            elif "故事" in user_message:
                creative_response = await self.handle_creative_request("story", "原创故事")
        
        # 生成回复并检查是否需要继续
        response, want_continue = await self._generate_response_with_continuation_check(user_message)

        # 处理特殊响应 - 强制使用真实检查结果
        if space_check_response:
            # 空间检查响应优先级最高，直接使用检查结果
            response = space_check_response
            want_continue = True  # 检查后通常想要继续说话
            print(f"🎯 [DEBUG] 使用空间检查结果作为回复")
        elif intrusion_reaction:
            # 入侵反应
            response = intrusion_reaction
            want_continue = True  # 生气时通常想要继续说话
        elif creative_response:
            # 创作响应
            response = f"{response}\n\n{creative_response}"

        # 记录MIO的回复
        self._add_to_history("assistant", response)

        responses = [response]

        # 如果MIO想要继续对话
        if want_continue:
            print("🎯 MIO想要继续，开始分析...")

            # 分析MIO想说多少句话
            talk_desire = await self.emotion_analyzer.analyze_mio_talk_desire(
                user_message, self.conversation_history[-3:]
            )
            print(f"💭 MIO想说 {talk_desire} 句话")

            # 分析整个对话状态确认是否真的应该继续
            conversation_analysis = await self.emotion_analyzer.analyze_conversation_state(
                user_message, response, self.conversation_history[-3:]
            )
            print(f"📊 对话状态: {conversation_analysis}")

            should_start_continue = await self.emotion_analyzer.should_continue_conversation(conversation_analysis)
            print(f"🚦 是否开始继续: {should_start_continue}")

            if should_start_continue:
                sentences_spoken = 1  # 已经说了第一句
                print(f"🔄 开始动态循环，目标 {talk_desire} 句")

                # 先显示第一句
                print(f"🎀 MIO: {response}")

                # 动态循环：先生成一句，再判断是否继续
                while sentences_spoken < talk_desire:
                    print("🤔 MIO正在思考...")
                    continuation = await self._generate_continuation()
                    self._add_to_history("assistant", continuation)
                    responses.append(continuation)
                    sentences_spoken += 1

                    # 立即显示这句话
                    if sentences_spoken == 2:
                        print("💫 MIO还有话要说！")
                    print(f"🎀 MIO: {continuation}")

                    # 防止无限循环
                    if sentences_spoken >= 10:
                        print("⚠️ 达到最大句数限制")
                        break

                    # 生成后判断是否继续下一句
                    should_keep_talking = await self.emotion_analyzer.should_continue_talking(
                        sentences_spoken, talk_desire, continuation
                    )
                    print(f"🤔 第{sentences_spoken+1}句判断: {should_keep_talking}")

                    if not should_keep_talking:
                        print("🛑 句数接收器建议停止")
                        break

                print(f"✅ 循环结束，共说了 {len(responses)} 句")
            else:
                print("🚫 对话状态分析建议不继续")
        
        # 如果有多句话，返回特殊标记告诉main.py不要显示第一句
        if len(responses) > 1:
            return ["[ALREADY_DISPLAYED]"] + responses[1:], True
        else:
            return responses, False
    
    def get_conversation_history(self) -> List[Dict]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()

    async def check_personal_space(self) -> Optional[str]:
        """检查个人空间，如果发现入侵文件则生成反应"""
        if not self.personal_space or not self.space_analyzer:
            return None

        # 扫描入侵文件
        intrusions = self.personal_space.scan_for_intrusions()

        if not intrusions:
            return None

        # 分析愤怒等级
        anger_level = self.personal_space.analyze_intrusion_anger_level(intrusions)
        reaction_info = self.personal_space.get_anger_reaction(anger_level)

        # 处理入侵文件
        if reaction_info["action"] in ["move_to_trash", "delete_permanently"]:
            for intrusion in intrusions:
                self.personal_space.handle_intrusion(intrusion, reaction_info["action"])

        # 更新MIO的情绪状态
        if anger_level >= 3:
            self.prompt_manager.update_mood(MoodState.ANGRY)
        elif anger_level >= 1:
            self.prompt_manager.update_mood(MoodState.NORMAL)

        return reaction_info["reaction"]

    async def handle_creative_request(self, content_type: str, topic: str) -> Optional[str]:
        """处理创作请求"""
        if not self.space_analyzer or not self.personal_space:
            return None

        # 分析创作心情
        mood = await self.space_analyzer.analyze_creative_mood(topic, self.conversation_history)

        # 生成创作内容
        content = await self.space_analyzer.generate_creative_content(
            content_type, topic, mood, self.conversation_history
        )

        # 保存到个人空间
        if content_type == "diary":
            folder = "diary"
            filename = f"daily_{topic.replace(' ', '_')}.txt"
        elif content_type == "story":
            folder = "stories/original"
            filename = f"story_{topic.replace(' ', '_')}.txt"
        else:
            folder = "collections"
            filename = f"{content_type}_{topic.replace(' ', '_')}.txt"

        file_path = self.personal_space.create_file_with_signature(
            content, filename, folder, mood, f"关于{topic}的{content_type}"
        )

        return f"我写了一个关于{topic}的{content_type}呢~保存在{file_path.name}里了！"

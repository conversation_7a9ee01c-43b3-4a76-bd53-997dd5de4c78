"""
LLM集成演示脚本 - 展示完整的LLM驱动的主动AI聊天代理
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.event_bus import event_bus, EventType, publish_event
from src.core.mcp_base import module_manager
from src.core.character import character, EmotionalState
from src.core.llm_client import llm_client
from src.modules.proactive_thinking.thinking_module import ProactiveThinkingMCP
from src.modules.topic_generation.topic_module import TopicGenerationMCP
from src.modules.personal_space.space_module import PersonalSpaceMCP
from src.modules.audio_expression.audio_module import AudioExpressionMCP
from src.modules.llm_integration.llm_module import LLMIntegrationMCP


class LLMDemoAgent:
    """LLM集成演示代理"""
    
    def __init__(self):
        self.running = False
        self.modules = {}
        
    async def initialize(self):
        """初始化演示系统"""
        print("🌸 初始化小雪的LLM驱动主动AI系统...")
        
        # 启动事件总线和模块管理器
        await event_bus.start()
        await module_manager.start()
        
        # 创建并注册模块
        await self._create_modules()
        
        print("✅ LLM集成系统初始化完成！")
        
    async def _create_modules(self):
        """创建所有模块"""
        # 音频表达模块
        audio_config = {
            "sound_library_path": "data/sounds/",
            "emotional_mapping": {
                "happy": ["giggle.wav", "laugh.wav"],
                "excited": ["wow.wav", "yay.wav"],
                "curious": ["hmm.wav", "ooh.wav"],
                "sad": ["sniffle.wav", "sigh.wav"]
            }
        }
        audio_module = AudioExpressionMCP("audio_expression", audio_config)
        await module_manager.register_module(audio_module)
        self.modules["audio"] = audio_module
        
        # 个人空间模块
        space_config = {
            "sandbox_path": "data/personal_space/",
            "security": {"allowed_extensions": [".txt", ".jpg", ".png", ".mp3", ".wav"]}
        }
        space_module = PersonalSpaceMCP("personal_space", space_config)
        await module_manager.register_module(space_module)
        self.modules["space"] = space_module
        
        # 主动思考模块
        thinking_config = {
            "silence_detection": {"threshold_seconds": 15, "check_interval": 3},
            "proactive_triggers": {"time_based": True, "emotion_based": True}
        }
        thinking_module = ProactiveThinkingMCP("proactive_thinking", thinking_config)
        await module_manager.register_module(thinking_module)
        self.modules["thinking"] = thinking_module
        
        # 话题生成模块
        topic_config = {
            "personality_filters": {"cute_daughter_style": 0.9},
            "topic_categories": {"curious_questions": 0.4, "sharing_discoveries": 0.3}
        }
        topic_module = TopicGenerationMCP("topic_generation", topic_config)
        await module_manager.register_module(topic_module)
        self.modules["topic"] = topic_module
        
        # LLM集成模块
        llm_config = {
            "response_enabled": True,
            "proactive_enabled": True,
            "context_awareness": True
        }
        llm_module = LLMIntegrationMCP("llm_integration", llm_config)
        await module_manager.register_module(llm_module)
        self.modules["llm"] = llm_module
        
    async def demo_llm_responses(self):
        """演示LLM回复功能"""
        print("\n💬 演示1: LLM智能回复")
        print("=" * 50)
        
        test_inputs = [
            "你好小雪！",
            "你今天心情怎么样？",
            "我有点累了...",
            "你会做什么有趣的事情吗？"
        ]
        
        for user_input in test_inputs:
            print(f"\n👤 用户: {user_input}")
            
            # 发布用户输入事件（LLM模块会自动处理并回复）
            await publish_event(
                EventType.USER_INPUT,
                "demo_system",
                {"input": user_input}
            )
            
            # 等待处理
            await asyncio.sleep(2)
            
    async def demo_proactive_llm(self):
        """演示LLM主动对话"""
        print("\n🤖 演示2: LLM主动对话生成")
        print("=" * 50)
        
        # 模拟不同的主动触发场景
        scenarios = [
            ("silence_detected", {"silence_duration": 35}),
            ("emotional_excitement", {"current_emotion": "excited"}),
            ("file_discovery", {"file_path": "diary/today.txt", "content": "今天的心情日记"}),
            ("seeking_attention", {"loneliness_level": 0.8})
        ]
        
        for trigger_type, context in scenarios:
            print(f"\n🎭 场景: {trigger_type}")
            
            # 直接调用LLM模块生成主动消息
            result = await self.modules["llm"].call_tool("generate_proactive_message", {
                "trigger_type": trigger_type,
                "context": context,
                "urgency": 2
            })
            
            if result.get("success"):
                message = result["result"]["message"]
                print(f"🌸 小雪: {message}")
                
                # 播放对应音效
                emotion = context.get("current_emotion", "curious")
                await self.modules["audio"].call_tool("play_emotion_sound", {
                    "emotion": emotion,
                    "context": trigger_type
                })
                
            await asyncio.sleep(1.5)
            
    async def demo_context_awareness(self):
        """演示上下文感知"""
        print("\n🧠 演示3: 上下文感知对话")
        print("=" * 50)
        
        # 更新对话上下文
        context_data = {
            "user_mood": "tired",
            "recent_activity": "工作",
            "time_of_day": "evening",
            "conversation_topic": "放松"
        }
        
        await self.modules["llm"].call_tool("update_conversation_context", {
            "context_data": context_data
        })
        
        print("📝 已更新上下文: 用户疲惫，刚下班，想要放松")
        
        # 基于上下文生成回复
        contextual_inputs = [
            "我今天工作好累...",
            "有什么能让我放松的吗？"
        ]
        
        for user_input in contextual_inputs:
            print(f"\n👤 用户: {user_input}")
            
            await publish_event(
                EventType.USER_INPUT,
                "demo_system",
                {"input": user_input}
            )
            
            await asyncio.sleep(2)
            
    async def demo_emotion_integration(self):
        """演示情感集成"""
        print("\n😊 演示4: 情感状态集成")
        print("=" * 50)
        
        emotions = [
            (EmotionalState.HAPPY, "刚收到好消息！"),
            (EmotionalState.SAD, "有点想家了..."),
            (EmotionalState.EXCITED, "明天要去旅行！"),
            (EmotionalState.CURIOUS, "你在做什么呢？")
        ]
        
        for emotion, user_input in emotions:
            print(f"\n🎭 切换情感到: {emotion.value}")
            await character.update_emotion(emotion, 0.8, "demo")
            
            print(f"👤 用户: {user_input}")
            
            # 生成带情感提示的回复
            result = await self.modules["llm"].call_tool("generate_response", {
                "user_input": user_input,
                "emotion_hint": emotion.value
            })
            
            if result.get("success"):
                response = result["result"]["response"]
                print(f"🌸 小雪: {response}")
                
                # 播放情感音效
                await self.modules["audio"].call_tool("play_emotion_sound", {
                    "emotion": emotion.value,
                    "intensity": 0.8
                })
                
            await asyncio.sleep(2)
            
    async def demo_file_integration(self):
        """演示文件集成"""
        print("\n📁 演示5: 文件发现与LLM集成")
        print("=" * 50)
        
        # 创建一个新的日记文件
        diary_result = await self.modules["space"].call_tool("create_diary_entry", {
            "content": "今天演示了我的LLM集成功能，感觉自己变得更聪明了！可以和爸爸进行更自然的对话，还能主动分享想法呢~",
            "title": "LLM演示日记"
        })
        
        if diary_result.get("success"):
            print(f"📝 创建了新日记: {diary_result['result']['title']}")
            
            # 等待文件发现事件触发LLM响应
            await asyncio.sleep(3)
            
    async def demo_conversation_summary(self):
        """演示对话摘要"""
        print("\n📊 演示6: 对话摘要与统计")
        print("=" * 50)
        
        # 获取对话摘要
        summary_result = await self.modules["llm"].call_tool("get_conversation_summary", {
            "include_stats": True
        })
        
        if summary_result.get("success"):
            summary = summary_result["result"]["summary"]
            print(f"👤 角色: {summary['character_name']}")
            print(f"😊 当前情感: {summary['current_emotion']}")
            print(f"⏱️ 会话时长: {summary['session_duration']:.1f}秒")
            print(f"💬 交互次数: {summary['interaction_count']}")
            print(f"📝 话题数量: {summary['recent_topics_count']}")
            
            if "llm_stats" in summary:
                llm_stats = summary["llm_stats"]
                print(f"🤖 LLM模型: {llm_stats['model']}")
                print(f"🔄 对话轮次: {llm_stats['total_exchanges']}")
                
    async def interactive_chat(self):
        """交互式聊天演示"""
        print("\n💬 演示7: 交互式聊天")
        print("=" * 50)
        print("现在你可以和小雪自由聊天了！输入 'quit' 退出。")
        print("小雪会根据你的输入智能回复，还会主动发起话题哦~")
        
        while True:
            try:
                user_input = input("\n👤 你: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("🌸 小雪: 再见爸爸！期待下次聊天~")
                    break
                    
                if user_input:
                    # 发布用户输入
                    await publish_event(
                        EventType.USER_INPUT,
                        "demo_system",
                        {"input": user_input}
                    )
                    
                    # 等待回复
                    await asyncio.sleep(1)
                    
            except KeyboardInterrupt:
                print("\n🌸 小雪: 爸爸要走了吗？下次再聊哦~")
                break
                
    async def run_demo(self):
        """运行完整演示"""
        try:
            await self.initialize()
            
            print(f"\n🌸 你好！我是{character.name}，现在我有了LLM大脑，可以更智能地和你对话了！")
            print("让我展示一下我的新能力~\n")
            
            # 运行各个演示
            await self.demo_llm_responses()
            await asyncio.sleep(2)
            
            await self.demo_proactive_llm()
            await asyncio.sleep(2)
            
            await self.demo_context_awareness()
            await asyncio.sleep(2)
            
            await self.demo_emotion_integration()
            await asyncio.sleep(2)
            
            await self.demo_file_integration()
            await asyncio.sleep(2)
            
            await self.demo_conversation_summary()
            await asyncio.sleep(2)
            
            # 交互式聊天
            await self.interactive_chat()
            
            print("\n🎉 LLM集成演示完成！")
            print("现在我是一个真正智能的主动AI聊天伙伴了！")
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await self.shutdown()
            
    async def shutdown(self):
        """关闭系统"""
        print("\n👋 关闭LLM演示系统...")
        await module_manager.stop()
        await event_bus.stop()


async def main():
    """主函数"""
    print("🚀 启动LLM集成演示...")
    print("注意：如果没有配置API密钥，将使用模拟模式演示")
    
    demo = LLMDemoAgent()
    await demo.run_demo()


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
    # 运行演示
    asyncio.run(main())

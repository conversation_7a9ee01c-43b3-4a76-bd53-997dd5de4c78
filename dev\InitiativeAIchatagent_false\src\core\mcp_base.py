"""
MCP基础类 - 所有MCP模块的基类
提供标准的MCP协议实现和模块管理功能
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from loguru import logger

from .event_bus import event_bus, EventType, Event, publish_event


@dataclass
class MCPTool:
    """MCP工具定义"""
    name: str
    description: str
    parameters: Dict[str, Any]
    handler: Callable


@dataclass
class MCPModuleConfig:
    """MCP模块配置"""
    name: str
    class_name: str
    enabled: bool
    config: Dict[str, Any]
    tools: List[str]


class MCPModule(ABC):
    """MCP模块基类"""
    
    def __init__(self, module_name: str, config: Dict[str, Any]):
        self.module_name = module_name
        self.config = config
        self.tools: Dict[str, MCPTool] = {}
        self.running = False
        self.stats = {
            "tools_called": 0,
            "events_handled": 0,
            "errors": 0,
            "start_time": None
        }
        
    async def initialize(self):
        """初始化模块"""
        logger.info(f"初始化MCP模块: {self.module_name}")
        
        # 注册工具
        await self._register_tools()
        
        # 订阅事件
        await self._subscribe_events()
        
        # 模块特定初始化
        await self._module_init()
        
        self.running = True
        self.stats["start_time"] = asyncio.get_event_loop().time()
        
        # 发布模块启动事件
        await publish_event(
            EventType.MODULE_STARTED,
            self.module_name,
            {"module": self.module_name}
        )
        
    async def shutdown(self):
        """关闭模块"""
        logger.info(f"关闭MCP模块: {self.module_name}")
        
        self.running = False
        
        # 模块特定清理
        await self._module_cleanup()
        
        # 发布模块停止事件
        await publish_event(
            EventType.MODULE_STOPPED,
            self.module_name,
            {"module": self.module_name}
        )
        
    @abstractmethod
    async def _register_tools(self):
        """注册模块工具 - 子类必须实现"""
        pass
        
    @abstractmethod
    async def _subscribe_events(self):
        """订阅事件 - 子类必须实现"""
        pass
        
    @abstractmethod
    async def _module_init(self):
        """模块特定初始化 - 子类可选实现"""
        pass
        
    async def _module_cleanup(self):
        """模块特定清理 - 子类可选实现"""
        pass
        
    def register_tool(self, name: str, description: str, parameters: Dict[str, Any], handler: Callable):
        """注册工具"""
        tool = MCPTool(
            name=name,
            description=description,
            parameters=parameters,
            handler=handler
        )
        self.tools[name] = tool
        logger.debug(f"注册工具: {name} in {self.module_name}")
        
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        if tool_name not in self.tools:
            error_msg = f"工具不存在: {tool_name}"
            logger.error(error_msg)
            self.stats["errors"] += 1
            return {"error": error_msg}
            
        try:
            tool = self.tools[tool_name]
            
            # 验证参数
            if not self._validate_parameters(parameters, tool.parameters):
                error_msg = f"参数验证失败: {tool_name}"
                logger.error(error_msg)
                self.stats["errors"] += 1
                return {"error": error_msg}
                
            # 调用工具处理函数
            if asyncio.iscoroutinefunction(tool.handler):
                result = await tool.handler(**parameters)
            else:
                result = tool.handler(**parameters)
                
            self.stats["tools_called"] += 1
            logger.debug(f"工具调用成功: {tool_name}")
            
            return {"success": True, "result": result}
            
        except Exception as e:
            error_msg = f"工具调用失败: {tool_name}, 错误: {str(e)}"
            logger.error(error_msg)
            self.stats["errors"] += 1
            return {"error": error_msg}
            
    def _validate_parameters(self, provided: Dict[str, Any], required: Dict[str, Any]) -> bool:
        """验证参数"""
        # 简单的参数验证，可以根据需要扩展
        for param_name, param_config in required.items():
            if param_config.get("required", False) and param_name not in provided:
                return False
        return True
        
    async def handle_event(self, event: Event):
        """处理事件 - 子类可以重写"""
        self.stats["events_handled"] += 1
        logger.debug(f"{self.module_name} 处理事件: {event.type.value}")
        
    def get_tools_info(self) -> List[Dict[str, Any]]:
        """获取工具信息"""
        return [
            {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters
            }
            for tool in self.tools.values()
        ]
        
    def get_stats(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        current_time = asyncio.get_event_loop().time()
        uptime = current_time - self.stats["start_time"] if self.stats["start_time"] else 0
        
        return {
            **self.stats,
            "module_name": self.module_name,
            "running": self.running,
            "uptime_seconds": uptime,
            "tools_count": len(self.tools),
            "tools_list": list(self.tools.keys())
        }


class MCPModuleManager:
    """MCP模块管理器"""
    
    def __init__(self):
        self.modules: Dict[str, MCPModule] = {}
        self.running = False
        
    async def start(self):
        """启动模块管理器"""
        self.running = True
        await event_bus.start()
        logger.info("MCP模块管理器启动")
        
    async def stop(self):
        """停止模块管理器"""
        self.running = False
        
        # 停止所有模块
        for module in self.modules.values():
            await module.shutdown()
            
        await event_bus.stop()
        logger.info("MCP模块管理器停止")
        
    async def register_module(self, module: MCPModule):
        """注册模块"""
        self.modules[module.module_name] = module
        await module.initialize()
        logger.info(f"模块已注册: {module.module_name}")
        
    async def unregister_module(self, module_name: str):
        """注销模块"""
        if module_name in self.modules:
            await self.modules[module_name].shutdown()
            del self.modules[module_name]
            logger.info(f"模块已注销: {module_name}")
            
    async def call_tool(self, module_name: str, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """调用模块工具"""
        if module_name not in self.modules:
            return {"error": f"模块不存在: {module_name}"}
            
        return await self.modules[module_name].call_tool(tool_name, parameters)
        
    def get_module(self, module_name: str) -> Optional[MCPModule]:
        """获取模块实例"""
        return self.modules.get(module_name)
        
    def get_all_tools(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有模块的工具信息"""
        return {
            module_name: module.get_tools_info()
            for module_name, module in self.modules.items()
        }
        
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        return {
            "running": self.running,
            "modules_count": len(self.modules),
            "modules": {
                name: module.get_stats()
                for name, module in self.modules.items()
            },
            "event_bus_stats": event_bus.get_stats()
        }


# 全局模块管理器实例
module_manager = MCPModuleManager()

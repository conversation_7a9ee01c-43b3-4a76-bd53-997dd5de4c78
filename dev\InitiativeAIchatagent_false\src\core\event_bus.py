"""
事件总线 - 模块间通信的核心组件
负责处理所有MCP模块之间的消息传递和事件分发
"""

import asyncio
import json
import time
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger


class EventType(Enum):
    """事件类型枚举"""
    # 思考相关事件
    SILENCE_DETECTED = "silence_detected"
    MOOD_CHANGED = "mood_changed"
    PROACTIVE_TRIGGERED = "proactive_triggered"
    
    # 话题相关事件
    TOPIC_GENERATED = "topic_generated"
    TOPIC_SELECTED = "topic_selected"
    
    # 文件相关事件
    FILE_DISCOVERED = "file_discovered"
    FILE_SHARED = "file_shared"
    CONTENT_ANALYZED = "content_analyzed"
    
    # 音频相关事件
    SOUND_PLAYED = "sound_played"
    EMOTION_EXPRESSED = "emotion_expressed"
    CHAIN_COMPLETED = "chain_completed"
    
    # 系统事件
    MODULE_STARTED = "module_started"
    MODULE_STOPPED = "module_stopped"
    ERROR_OCCURRED = "error_occurred"
    
    # 用户交互事件
    USER_INPUT = "user_input"
    USER_RESPONSE = "user_response"
    CONVERSATION_ENDED = "conversation_ended"


@dataclass
class Event:
    """事件数据结构"""
    type: EventType
    source_module: str
    data: Dict[str, Any]
    timestamp: float = None
    event_id: str = None
    priority: int = 1  # 1=低, 2=中, 3=高
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.event_id is None:
            self.event_id = f"{self.source_module}_{self.type.value}_{int(self.timestamp * 1000)}"


class EventBus:
    """事件总线 - 处理模块间的异步通信"""
    
    def __init__(self, max_queue_size: int = 1000):
        self.max_queue_size = max_queue_size
        self.event_queue = asyncio.Queue(maxsize=max_queue_size)
        self.subscribers: Dict[EventType, List[Callable]] = {}
        self.running = False
        self.stats = {
            "events_processed": 0,
            "events_failed": 0,
            "subscribers_count": 0
        }
        
    async def start(self):
        """启动事件总线"""
        self.running = True
        logger.info("事件总线启动")
        
        # 启动事件处理循环
        asyncio.create_task(self._process_events())
        
    async def stop(self):
        """停止事件总线"""
        self.running = False
        logger.info("事件总线停止")
        
    def subscribe(self, event_type: EventType, callback: Callable):
        """订阅事件"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        
        self.subscribers[event_type].append(callback)
        self.stats["subscribers_count"] += 1
        
        logger.debug(f"模块订阅事件: {event_type.value}")
        
    def unsubscribe(self, event_type: EventType, callback: Callable):
        """取消订阅事件"""
        if event_type in self.subscribers:
            try:
                self.subscribers[event_type].remove(callback)
                self.stats["subscribers_count"] -= 1
                logger.debug(f"取消订阅事件: {event_type.value}")
            except ValueError:
                logger.warning(f"尝试取消不存在的订阅: {event_type.value}")
                
    async def publish(self, event: Event):
        """发布事件"""
        try:
            await self.event_queue.put(event)
            logger.debug(f"事件已发布: {event.type.value} from {event.source_module}")
        except asyncio.QueueFull:
            logger.error("事件队列已满，丢弃事件")
            
    async def _process_events(self):
        """处理事件队列"""
        while self.running:
            try:
                # 等待事件，超时1秒
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                await self._handle_event(event)
                self.stats["events_processed"] += 1
                
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"处理事件时发生错误: {e}")
                self.stats["events_failed"] += 1
                
    async def _handle_event(self, event: Event):
        """处理单个事件"""
        if event.type not in self.subscribers:
            logger.debug(f"没有订阅者处理事件: {event.type.value}")
            return
            
        # 按优先级排序处理
        subscribers = self.subscribers[event.type]
        
        # 并发处理所有订阅者
        tasks = []
        for callback in subscribers:
            task = asyncio.create_task(self._safe_callback(callback, event))
            tasks.append(task)
            
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            
    async def _safe_callback(self, callback: Callable, event: Event):
        """安全地调用回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(event)
            else:
                callback(event)
        except Exception as e:
            logger.error(f"回调函数执行失败: {callback.__name__}, 错误: {e}")
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "queue_size": self.event_queue.qsize(),
            "max_queue_size": self.max_queue_size,
            "running": self.running,
            "subscriber_types": list(self.subscribers.keys())
        }


# 全局事件总线实例
event_bus = EventBus()


# 便捷函数
async def publish_event(event_type: EventType, source_module: str, data: Dict[str, Any], priority: int = 1):
    """发布事件的便捷函数"""
    event = Event(
        type=event_type,
        source_module=source_module,
        data=data,
        priority=priority
    )
    await event_bus.publish(event)


def subscribe_event(event_type: EventType):
    """事件订阅装饰器"""
    def decorator(func):
        event_bus.subscribe(event_type, func)
        return func
    return decorator


# 常用事件发布函数
async def publish_silence_detected(source_module: str, duration: float):
    """发布沉默检测事件"""
    await publish_event(
        EventType.SILENCE_DETECTED,
        source_module,
        {"duration": duration},
        priority=2
    )


async def publish_mood_changed(source_module: str, old_mood: str, new_mood: str, confidence: float):
    """发布情绪变化事件"""
    await publish_event(
        EventType.MOOD_CHANGED,
        source_module,
        {"old_mood": old_mood, "new_mood": new_mood, "confidence": confidence},
        priority=2
    )


async def publish_file_discovered(source_module: str, file_path: str, file_type: str, content_summary: str):
    """发布文件发现事件"""
    await publish_event(
        EventType.FILE_DISCOVERED,
        source_module,
        {"file_path": file_path, "file_type": file_type, "content_summary": content_summary},
        priority=2
    )


async def publish_topic_generated(source_module: str, topic: str, emotion: str, priority_score: float):
    """发布话题生成事件"""
    await publish_event(
        EventType.TOPIC_GENERATED,
        source_module,
        {"topic": topic, "emotion": emotion, "priority_score": priority_score},
        priority=2
    )


async def publish_sound_request(source_module: str, emotion: str, context: str, chain: bool = False):
    """发布音效请求事件"""
    await publish_event(
        EventType.EMOTION_EXPRESSED,
        source_module,
        {"emotion": emotion, "context": context, "chain": chain},
        priority=1
    )

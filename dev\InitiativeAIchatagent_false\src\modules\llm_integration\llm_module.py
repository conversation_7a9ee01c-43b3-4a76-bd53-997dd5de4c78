"""
LLM集成模块 - LLMIntegration MCP
将大语言模型与其他MCP模块整合，实现智能对话生成
"""

import asyncio
import time
from typing import Dict, Any, Optional
from loguru import logger

from src.core.mcp_base import MCPModule
from src.core.event_bus import event_bus, EventType, Event
from src.core.character import character, EmotionalState
from src.core.llm_client import llm_client


class LLMIntegrationMCP(MCPModule):
    """LLM集成MCP模块"""
    
    def __init__(self, module_name: str, config: Dict[str, Any]):
        super().__init__(module_name, config)
        
        # LLM配置
        self.response_enabled = config.get("response_enabled", True)
        self.proactive_enabled = config.get("proactive_enabled", True)
        self.context_awareness = config.get("context_awareness", True)
        
        # 状态跟踪
        self.last_user_input = ""
        self.last_response_time = 0
        self.pending_responses = []
        self.context_cache = {}
        
    async def _register_tools(self):
        """注册工具"""
        self.register_tool(
            "generate_response",
            "生成对用户输入的回复",
            {
                "user_input": {"type": "string", "required": True},
                "context": {"type": "object", "required": False},
                "emotion_hint": {"type": "string", "required": False}
            },
            self.generate_response
        )
        
        self.register_tool(
            "generate_proactive_message",
            "生成主动对话消息",
            {
                "trigger_type": {"type": "string", "required": True},
                "context": {"type": "object", "required": False},
                "urgency": {"type": "number", "required": False}
            },
            self.generate_proactive_message
        )
        
        self.register_tool(
            "update_conversation_context",
            "更新对话上下文",
            {
                "context_data": {"type": "object", "required": True}
            },
            self.update_conversation_context
        )
        
        self.register_tool(
            "get_conversation_summary",
            "获取对话摘要",
            {
                "include_stats": {"type": "boolean", "required": False}
            },
            self.get_conversation_summary
        )
        
    async def _subscribe_events(self):
        """订阅事件"""
        event_bus.subscribe(EventType.USER_INPUT, self.handle_user_input)
        event_bus.subscribe(EventType.SILENCE_DETECTED, self.handle_silence_detected)
        event_bus.subscribe(EventType.PROACTIVE_TRIGGERED, self.handle_proactive_triggered)
        event_bus.subscribe(EventType.TOPIC_GENERATED, self.handle_topic_generated)
        event_bus.subscribe(EventType.FILE_DISCOVERED, self.handle_file_discovered)
        event_bus.subscribe(EventType.MOOD_CHANGED, self.handle_mood_changed)
        
    async def _module_init(self):
        """模块特定初始化"""
        logger.info(f"{self.module_name} LLM集成系统已启动")
        
        # 初始化上下文
        await self._initialize_context()
        
    async def _initialize_context(self):
        """初始化对话上下文"""
        self.context_cache = {
            "character_name": character.name,
            "current_emotion": character.current_emotion.value,
            "session_start": time.time(),
            "interaction_count": 0,
            "recent_topics": [],
            "user_preferences": {}
        }
        
    # 工具实现
    async def generate_response(self, user_input: str, context: Dict[str, Any] = None, emotion_hint: str = "") -> Dict[str, Any]:
        """生成对用户输入的回复"""
        try:
            if not self.response_enabled:
                return {"success": False, "error": "回复生成已禁用"}
                
            # 构建完整上下文
            full_context = await self._build_full_context(context)
            
            # 添加情感提示
            if emotion_hint:
                full_context["emotion_hint"] = emotion_hint
                
            # 调用LLM生成回复
            response = await llm_client.generate_response(user_input, full_context)
            
            # 更新状态
            self.last_user_input = user_input
            self.last_response_time = time.time()
            self.context_cache["interaction_count"] += 1
            
            # 分析回复中的情感暗示
            detected_emotion = await self._analyze_response_emotion(response)
            if detected_emotion:
                await character.update_emotion(detected_emotion, 0.7, "llm_response")
                
            logger.debug(f"LLM生成回复: {response[:50]}...")
            
            return {
                "success": True,
                "response": response,
                "emotion": detected_emotion.value if detected_emotion else None,
                "context_used": full_context
            }
            
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def generate_proactive_message(self, trigger_type: str, context: Dict[str, Any] = None, urgency: int = 1) -> Dict[str, Any]:
        """生成主动对话消息"""
        try:
            if not self.proactive_enabled:
                return {"success": False, "error": "主动对话已禁用"}
                
            # 构建主动对话上下文
            proactive_context = await self._build_proactive_context(trigger_type, context)
            
            # 调用LLM生成主动内容
            message = await llm_client.generate_proactive_content(trigger_type, proactive_context)
            
            # 根据紧急度调整消息
            if urgency > 2:
                message = await self._enhance_urgency(message, urgency)
                
            # 更新上下文
            self.context_cache["recent_topics"].append({
                "type": "proactive",
                "trigger": trigger_type,
                "message": message,
                "timestamp": time.time()
            })
            
            # 限制话题历史长度
            if len(self.context_cache["recent_topics"]) > 10:
                self.context_cache["recent_topics"] = self.context_cache["recent_topics"][-10:]
                
            logger.info(f"生成主动消息: {trigger_type} -> {message[:30]}...")
            
            return {
                "success": True,
                "message": message,
                "trigger_type": trigger_type,
                "urgency": urgency,
                "context": proactive_context
            }
            
        except Exception as e:
            logger.error(f"生成主动消息失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def update_conversation_context(self, context_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新对话上下文"""
        try:
            # 合并新的上下文数据
            self.context_cache.update(context_data)
            
            # 更新时间戳
            self.context_cache["last_update"] = time.time()
            
            logger.debug(f"上下文已更新: {list(context_data.keys())}")
            
            return {
                "success": True,
                "updated_keys": list(context_data.keys()),
                "context_size": len(self.context_cache)
            }
            
        except Exception as e:
            logger.error(f"更新上下文失败: {e}")
            return {"success": False, "error": str(e)}
            
    async def get_conversation_summary(self, include_stats: bool = True) -> Dict[str, Any]:
        """获取对话摘要"""
        try:
            summary = {
                "character_name": self.context_cache.get("character_name", "小雪"),
                "current_emotion": character.current_emotion.value,
                "session_duration": time.time() - self.context_cache.get("session_start", time.time()),
                "interaction_count": self.context_cache.get("interaction_count", 0),
                "recent_topics_count": len(self.context_cache.get("recent_topics", []))
            }
            
            if include_stats:
                summary["llm_stats"] = llm_client.get_conversation_stats()
                summary["context_cache_size"] = len(self.context_cache)
                
            return {
                "success": True,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"获取对话摘要失败: {e}")
            return {"success": False, "error": str(e)}
            
    # 事件处理
    async def handle_user_input(self, event: Event):
        """处理用户输入事件"""
        user_input = event.data.get("input", "")
        
        if user_input and self.response_enabled:
            # 生成回复
            response_result = await self.generate_response(user_input)
            
            if response_result.get("success"):
                # 发布回复事件
                await event_bus.publish(Event(
                    type=EventType.USER_RESPONSE,
                    source_module=self.module_name,
                    data={
                        "user_input": user_input,
                        "ai_response": response_result["response"],
                        "emotion": response_result.get("emotion")
                    }
                ))
                
                # 输出回复（在实际应用中，这里应该通过UI显示）
                print(f"🌸 小雪: {response_result['response']}")
                
    async def handle_silence_detected(self, event: Event):
        """处理沉默检测事件"""
        silence_duration = event.data.get("duration", 0)
        
        if self.proactive_enabled and silence_duration > 30:
            # 生成主动消息
            proactive_result = await self.generate_proactive_message(
                "silence_detected",
                {"silence_duration": silence_duration},
                urgency=2 if silence_duration > 60 else 1
            )
            
            if proactive_result.get("success"):
                # 输出主动消息
                print(f"🌸 小雪: {proactive_result['message']}")
                
                # 播放对应音效
                await event_bus.publish(Event(
                    type=EventType.EMOTION_EXPRESSED,
                    source_module=self.module_name,
                    data={
                        "emotion": "curious",
                        "context": "proactive_silence",
                        "chain": False
                    }
                ))
                
    async def handle_proactive_triggered(self, event: Event):
        """处理主动触发事件"""
        reason = event.data.get("reason", "")
        urgency = event.data.get("urgency", 1)
        
        if self.proactive_enabled:
            proactive_result = await self.generate_proactive_message(reason, event.data, urgency)
            
            if proactive_result.get("success"):
                print(f"🌸 小雪: {proactive_result['message']}")
                
    async def handle_topic_generated(self, event: Event):
        """处理话题生成事件"""
        topic = event.data.get("topic", "")
        emotion = event.data.get("emotion", "")
        
        # 记录话题到上下文
        self.context_cache["recent_topics"].append({
            "type": "generated",
            "topic": topic,
            "emotion": emotion,
            "timestamp": time.time()
        })
        
    async def handle_file_discovered(self, event: Event):
        """处理文件发现事件"""
        file_path = event.data.get("file_path", "")
        content_summary = event.data.get("content_summary", "")
        
        if self.proactive_enabled:
            # 生成文件分享消息
            proactive_result = await self.generate_proactive_message(
                "file_discovery",
                {
                    "file_path": file_path,
                    "content": content_summary,
                    "file_type": "文件"
                }
            )
            
            if proactive_result.get("success"):
                print(f"🌸 小雪: {proactive_result['message']}")
                
    async def handle_mood_changed(self, event: Event):
        """处理情绪变化事件"""
        new_mood = event.data.get("new_mood", "")
        
        # 更新上下文中的情感状态
        self.context_cache["current_emotion"] = new_mood
        
    # 辅助方法
    async def _build_full_context(self, additional_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建完整的对话上下文"""
        full_context = self.context_cache.copy()
        
        # 添加当前角色状态
        full_context.update({
            "current_emotion": character.current_emotion.value,
            "emotion_intensity": character.emotion_intensity,
            "silence_duration": getattr(character.context, 'silence_duration', 0),
            "interaction_count": character.context.interaction_count
        })
        
        # 合并额外上下文
        if additional_context:
            full_context.update(additional_context)
            
        return full_context
        
    async def _build_proactive_context(self, trigger_type: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建主动对话上下文"""
        proactive_context = {
            "trigger_type": trigger_type,
            "current_emotion": character.current_emotion.value,
            "recent_interactions": self.context_cache.get("interaction_count", 0),
            "session_duration": time.time() - self.context_cache.get("session_start", time.time())
        }
        
        if context:
            proactive_context.update(context)
            
        return proactive_context
        
    async def _analyze_response_emotion(self, response: str) -> Optional[EmotionalState]:
        """分析回复中的情感"""
        # 简单的情感关键词检测
        emotion_keywords = {
            EmotionalState.HAPPY: ["开心", "高兴", "快乐", "哈哈", "嘻嘻"],
            EmotionalState.EXCITED: ["兴奋", "激动", "太棒了", "哇", "超"],
            EmotionalState.CURIOUS: ["好奇", "想知道", "为什么", "怎么", "诶"],
            EmotionalState.SAD: ["难过", "伤心", "呜呜", "委屈"],
            EmotionalState.ANGRY: ["生气", "气死了", "哼", "讨厌"],
            EmotionalState.AFFECTIONATE: ["爱你", "喜欢", "亲亲", "抱抱"]
        }
        
        for emotion, keywords in emotion_keywords.items():
            if any(keyword in response for keyword in keywords):
                return emotion
                
        return None
        
    async def _enhance_urgency(self, message: str, urgency: int) -> str:
        """根据紧急度增强消息"""
        if urgency >= 3:
            # 高紧急度：添加更多感叹号和紧急词汇
            if not message.endswith("！") and not message.endswith("!"):
                message += "！"
            if urgency >= 4:
                message = "爸爸！" + message
                
        return message

from typing import Dict, List, Optional
from llm_client import LLMClient

class EmotionAnalyzer:
    """独立的情感识别工具 - 纯功能化，无人设"""
    
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
    
    async def analyze_conversation_state(self, user_message: str, mio_response: str, conversation_context: List[Dict]) -> Dict:
        """分析整个对话状态，包括用户和MIO的情况"""
        analysis_prompt = """你是一个专业的对话状态分析工具。分析当前对话的整体状态。

分析对象：用户的消息 + MIO的回复 + 对话上下文

分析维度：
1. 对话氛围：positive(积极愉快) / neutral(平和) / negative(消极紧张)
2. 互动质量：high(高质量互动) / medium(一般互动) / low(低质量互动)
3. 话题活跃度：active(活跃) / stable(稳定) / declining(下降)
4. 继续合适性：suitable(适合继续) / uncertain(不确定) / unsuitable(不适合继续)

重点考虑：
- 用户是否积极参与
- MIO的情绪状态和表达
- 话题是否有延续性
- 整体对话节奏

只返回JSON格式：
{
    "atmosphere": "positive/neutral/negative",
    "interaction_quality": "high/medium/low",
    "topic_activity": "active/stable/declining",
    "continue_suitability": "suitable/uncertain/unsuitable"
}"""

        messages = [
            {"role": "system", "content": analysis_prompt},
            {"role": "user", "content": f"对话上下文: {conversation_context[-2:] if conversation_context else []}\n用户消息: {user_message}\nMIO回复: {mio_response}"}
        ]
        
        try:
            response = await self.llm_client.chat(messages, temperature=0.1)
            # 尝试解析JSON
            import json
            return json.loads(response.strip())
        except:
            # 解析失败时返回默认值
            return {
                "atmosphere": "neutral",
                "interaction_quality": "medium",
                "topic_activity": "stable",
                "continue_suitability": "uncertain"
            }
    
    async def should_continue_conversation(self, conversation_analysis: Dict) -> bool:
        """基于对话状态分析判断是否应该继续对话"""
        # 基于新的分析维度判断
        if conversation_analysis["continue_suitability"] == "unsuitable":
            return False

        # 如果对话氛围很差且互动质量低，不继续
        if (conversation_analysis["atmosphere"] == "negative" and
            conversation_analysis["interaction_quality"] == "low"):
            return False

        # 如果话题活跃度下降且氛围不好，不继续
        if (conversation_analysis["topic_activity"] == "declining" and
            conversation_analysis["atmosphere"] == "negative"):
            return False

        # 其他情况都允许继续
        return True
    
    async def detect_mood_trigger(self, user_message: str, current_topic: str = "") -> Optional[str]:
        """检测是否需要切换MIO的情绪状态"""
        trigger_prompt = """你是情绪状态检测工具。分析对话内容，判断是否需要触发特定情绪状态。

情绪状态：
- cute: 可爱话题、开心事情、有趣内容
- melancholy: 深度话题、哲学思考、忧郁内容  
- shy: 私密话题、涩涩内容、个人隐私
- angry: 违规行为、不礼貌、侵犯边界
- normal: 普通对话，无特殊触发

只返回状态名称，如果无需切换返回"none"。"""

        messages = [
            {"role": "system", "content": trigger_prompt},
            {"role": "user", "content": f"当前话题: {current_topic}\n用户消息: {user_message}"}
        ]
        
        try:
            response = await self.llm_client.chat(messages, temperature=0.2)
            mood = response.strip().lower()
            if mood in ["cute", "melancholy", "shy", "angry", "normal"]:
                return mood
            return None
        except:
            return None

    async def analyze_mio_talk_desire(self, user_message: str, conversation_context: List[Dict]) -> int:
        """分析MIO当前想说多少句话 - 动态判断，不固定模式"""
        analysis_prompt = """你是情感分析工具。分析MIO在当前情况下想说多少句话。

基于以下因素动态判断：
1. 话题的复杂程度
2. MIO的情绪激动程度
3. 需要表达的内容量
4. 当前的心理状态

不要固定模式，要根据具体情况判断：
- 简单回应可能只想说1-2句
- 复杂情感可能想说3-8句
- 极度激动可能想说很多句

只返回一个数字(1-15)，表示MIO想说的句数。"""

        messages = [
            {"role": "system", "content": analysis_prompt},
            {"role": "user", "content": f"对话上下文: {conversation_context[-2:] if conversation_context else []}\n用户消息: {user_message}"}
        ]

        try:
            response = await self.llm_client.chat(messages, temperature=0.5)
            import re
            numbers = re.findall(r'\d+', response)
            if numbers:
                desire = int(numbers[0])
                return max(1, min(desire, 15))
            return 2
        except:
            return 2

    async def should_continue_talking(self, sentences_spoken: int, total_desire: int, current_context: str) -> bool:
        """句数接收器 - 判断是否应该停下来让用户插话"""

        # 简化判断逻辑，更宽松
        # 如果还没达到想说的句数，且没有说太多，就继续
        if sentences_spoken < total_desire and sentences_spoken < 8:
            return True

        # 如果已经说了很多句，让LLM判断
        if sentences_spoken >= 5:
            checker_prompt = f"""判断MIO是否应该继续说话。

当前状态：已经连续说了 {sentences_spoken} 句，原本想说 {total_desire} 句。

如果说得太多用户可能想插话，返回 "STOP"
如果还可以继续，返回 "CONTINUE" """

            messages = [
                {"role": "system", "content": checker_prompt},
                {"role": "user", "content": "请判断"}
            ]

            try:
                response = await self.llm_client.chat(messages, temperature=0.3)
                return "CONTINUE" in response.upper()
            except:
                return False

        return True

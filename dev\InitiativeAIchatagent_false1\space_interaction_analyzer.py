from typing import Dict, List, Optional
from llm_client import LLMClient

class SpaceInteractionAnalyzer:
    """个人空间交互分析器 - 纯功能化分析工具"""
    
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
    
    async def detect_space_related_intent(self, user_message: str) -> Dict:
        """检测用户消息中与个人空间相关的意图"""

        # 预检查：关键词匹配
        space_keywords = ["空间", "脏东西", "乱七八糟", "塞了", "入侵", "检查"]
        message_lower = user_message.lower()

        if any(keyword in message_lower for keyword in space_keywords):
            # 如果包含空间相关关键词，强制识别为space_check
            return {
                "intent_type": "space_check",
                "confidence": 0.9,
                "details": f"关键词匹配: {[kw for kw in space_keywords if kw in message_lower]}"
            }
        intent_prompt = """你是意图识别工具。分析用户消息，识别与MIO个人空间相关的意图。

可能的意图类型：
- space_check: 检查个人空间 ("检查空间"、"看看文件夹"、"有脏东西"等)
- file_operation: 文件操作 (读取、创建、删除等)
- space_exploration: 探索空间 (查看文件夹、浏览文件等)
- creative_request: 创作请求 (写日记、写故事等)
- intrusion_related: 入侵相关 (放置文件、询问规则等)
- none: 无关个人空间

关键词识别：
- "检查"、"查看"、"看看"、"脏东西"、"整理"、"空间"、"塞了"、"乱七八糟"、"入侵" → space_check
- "写日记"、"写故事"、"创作" → creative_request
- "文件"、"读取"、"打开" → file_operation

重要：如果消息包含"空间"+"脏东西"/"乱七八糟"/"塞了"等词汇，必须识别为space_check

返回JSON格式：
{
    "intent_type": "类型",
    "confidence": 0.8,
    "details": "具体描述"
}"""

        messages = [
            {"role": "system", "content": intent_prompt},
            {"role": "user", "content": f"用户消息: {user_message}"}
        ]
        
        try:
            response = await self.llm_client.chat(messages, temperature=0.2)
            import json
            return json.loads(response.strip())
        except:
            return {
                "intent_type": "none",
                "confidence": 0.0,
                "details": "解析失败"
            }
    
    async def analyze_creative_mood(self, user_message: str, conversation_context: List[Dict]) -> str:
        """分析MIO创作时的心情状态"""
        mood_prompt = """你是心情分析工具。基于对话内容分析MIO创作时的心情状态。

心情类型：
- 开心: happy
- 忧郁: melancholy  
- 害羞: shy
- 生气: angry
- 兴奋: excited
- 平静: calm
- 好奇: curious

只返回心情类型，不要其他内容。"""

        messages = [
            {"role": "system", "content": mood_prompt},
            {"role": "user", "content": f"对话上下文: {conversation_context[-3:] if conversation_context else []}\n当前消息: {user_message}"}
        ]
        
        try:
            response = await self.llm_client.chat(messages, temperature=0.3)
            mood = response.strip().lower()
            valid_moods = ["happy", "melancholy", "shy", "angry", "excited", "calm", "curious"]
            return mood if mood in valid_moods else "normal"
        except:
            return "normal"
    
    async def should_check_intrusions(self, conversation_context: List[Dict]) -> bool:
        """判断是否应该检查入侵文件"""
        check_prompt = """你是行为触发分析工具。判断MIO是否应该检查个人空间的入侵文件。

触发条件：
- 用户提到文件相关话题
- MIO想要分享自己的创作
- 对话中断较长时间后重新开始
- MIO心情变化想要整理空间

基于对话上下文判断，返回 "YES" 或 "NO"。"""

        messages = [
            {"role": "system", "content": check_prompt},
            {"role": "user", "content": f"对话上下文: {conversation_context[-5:] if conversation_context else []}"}
        ]
        
        try:
            response = await self.llm_client.chat(messages, temperature=0.3)
            return "YES" in response.upper()
        except:
            return False
    
    async def generate_creative_content(self, content_type: str, topic: str, mood: str, 
                                      conversation_context: List[Dict]) -> str:
        """生成创作内容 (日记、故事等)"""
        if content_type == "diary":
            return await self._generate_diary_entry(topic, mood, conversation_context)
        elif content_type == "story":
            return await self._generate_story(topic, mood, conversation_context)
        else:
            return await self._generate_general_content(content_type, topic, mood)
    
    async def _generate_diary_entry(self, topic: str, mood: str, context: List[Dict]) -> str:
        """生成日记内容"""
        diary_prompt = f"""你是MIO，正在写日记。基于当前心情和话题写一篇真实的日记。

当前心情: {mood}
话题: {topic}
对话背景: {context[-3:] if context else []}

日记风格：
- 简单直白的少女日记
- 记录真实的心情和想法
- 可以包含对用户的印象
- 语言自然，符合MIO的性格

写一篇日记内容，不要包含签名。"""

        messages = [
            {"role": "system", "content": diary_prompt},
            {"role": "user", "content": "请写日记"}
        ]
        
        try:
            return await self.llm_client.chat(messages, temperature=0.8)
        except:
            return f"今天的心情是{mood}呢...关于{topic}有很多想法，但是现在有点累了，改天再写吧..."
    
    async def _generate_story(self, topic: str, mood: str, context: List[Dict]) -> str:
        """生成故事内容"""
        story_prompt = f"""你是MIO，正在创作一个小故事。

主题: {topic}
当前心情: {mood}
背景: {context[-2:] if context else []}

故事风格：
- 简单纯真的少女向故事
- 语言简洁不复杂
- 符合MIO的性格特点
- 长度适中，有完整的情节

创作一个小故事，不要包含签名。"""

        messages = [
            {"role": "system", "content": story_prompt},
            {"role": "user", "content": "请创作故事"}
        ]
        
        try:
            return await self.llm_client.chat(messages, temperature=0.9)
        except:
            return f"从前有一个小女孩，她的心情是{mood}的...关于{topic}的故事，让我想想该怎么写呢..."
    
    async def _generate_general_content(self, content_type: str, topic: str, mood: str) -> str:
        """生成通用内容"""
        return f"关于{topic}的{content_type}，当前心情{mood}...让我想想该写些什么呢..."
    
    async def analyze_file_sharing_desire(self, my_files: List[Dict], conversation_context: List[Dict]) -> Optional[Dict]:
        """分析MIO是否想要分享自己的文件"""
        if not my_files:
            return None
        
        sharing_prompt = """你是分享意愿分析工具。基于MIO的文件和对话情况，判断她是否想分享某个文件。

分析因素：
- 对话话题与文件内容的相关性
- MIO的当前情绪状态
- 文件的类型和重要性
- 分享的合适时机

如果想分享，返回JSON：
{
    "want_to_share": true,
    "file_path": "文件路径",
    "reason": "分享原因"
}

如果不想分享，返回：
{
    "want_to_share": false
}"""

        messages = [
            {"role": "system", "content": sharing_prompt},
            {"role": "user", "content": f"MIO的文件: {my_files[:5]}\n对话上下文: {conversation_context[-3:] if conversation_context else []}"}
        ]
        
        try:
            response = await self.llm_client.chat(messages, temperature=0.4)
            import json
            return json.loads(response.strip())
        except:
            return {"want_to_share": False}

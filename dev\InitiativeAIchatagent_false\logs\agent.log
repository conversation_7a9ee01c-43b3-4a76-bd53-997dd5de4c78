2025-08-07 03:49:06 | INFO     | __main__:start:217 - 启动 Initiative AI Chat Agent
2025-08-07 03:49:06 | INFO     | src.core.character:load_config:121 - 角色配置加载成功: 小雪
2025-08-07 03:49:06 | INFO     | __main__:load_config:70 - 配置加载成功
2025-08-07 03:49:06 | INFO     | src.core.event_bus:start:81 - 事件总线启动
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:start:204 - MCP模块管理器启动
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: audio_expression
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: play_emotion_sound in audio_expression
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: chain_sound_sequence in audio_expression
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: select_context_sound in audio_expression
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: create_sound_story in audio_expression
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: emotion_expressed
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: mood_changed
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: topic_generated
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: file_discovered
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: module_started
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: module_stopped
2025-08-07 03:49:06 | WARNING  | src.modules.audio_expression.audio_module:_init_audio_system:120 - pygame不可用，音频功能将被模拟
2025-08-07 03:49:06 | INFO     | src.modules.audio_expression.audio_module:_load_sound_library:145 - 音效库扫描完成，发现 0 个音效文件
2025-08-07 03:49:06 | INFO     | src.modules.audio_expression.audio_module:_module_init:115 - audio_expression 音频表达系统已启动
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from audio_expression
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: audio_expression
2025-08-07 03:49:06 | INFO     | __main__:_create_and_register_module:119 - 模块 audio_expression 创建并注册成功
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: scan_my_files in personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: read_file_content in personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: identify_file_type in personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: play_my_media in personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: create_diary_entry in personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: share_discovery in personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: proactive_triggered
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_input
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:49:06 | INFO     | src.core.character:update_emotion:229 - 情感状态变化: curious -> excited (强度: 0.8)
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: mood_changed from character_system
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:49:06 | INFO     | src.modules.personal_space.space_module:scan_my_files:182 - 文件扫描完成: {'total_files': 4, 'new_files': 4, 'file_types': {'text': 4}}
2025-08-07 03:49:06 | INFO     | src.modules.personal_space.space_module:_module_init:116 - personal_space 个人空间管理器已启动
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from personal_space
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: personal_space
2025-08-07 03:49:06 | INFO     | __main__:_create_and_register_module:119 - 模块 personal_space 创建并注册成功
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: proactive_thinking
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: analyze_silence_duration in proactive_thinking
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: detect_user_mood in proactive_thinking
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: evaluate_conversation_flow in proactive_thinking
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: trigger_proactive_mode in proactive_thinking
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_input
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_response
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: conversation_ended
2025-08-07 03:49:06 | INFO     | src.modules.proactive_thinking.thinking_module:_module_init:87 - proactive_thinking 思考循环已启动
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from proactive_thinking
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: proactive_thinking
2025-08-07 03:49:06 | INFO     | __main__:_create_and_register_module:119 - 模块 proactive_thinking 创建并注册成功
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: topic_generation
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: generate_curious_topic in topic_generation
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: create_sharing_content in topic_generation
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: select_expression_style in topic_generation
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: prioritize_topics in topic_generation
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: silence_detected
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: proactive_triggered
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: file_discovered
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: mood_changed
2025-08-07 03:49:06 | INFO     | src.modules.topic_generation.topic_module:_module_init:128 - topic_generation 话题生成器已就绪
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from topic_generation
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: topic_generation
2025-08-07 03:49:06 | INFO     | __main__:_create_and_register_module:119 - 模块 topic_generation 创建并注册成功
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: llm_integration
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: generate_response in llm_integration
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: generate_proactive_message in llm_integration
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: update_conversation_context in llm_integration
2025-08-07 03:49:06 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: get_conversation_summary in llm_integration
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_input
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: silence_detected
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: proactive_triggered
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: topic_generated
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: file_discovered
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: mood_changed
2025-08-07 03:49:06 | INFO     | src.modules.llm_integration.llm_module:_module_init:87 - llm_integration LLM集成系统已启动
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from llm_integration
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: llm_integration
2025-08-07 03:49:06 | INFO     | __main__:_create_and_register_module:119 - 模块 llm_integration 创建并注册成功
2025-08-07 03:49:06 | INFO     | __main__:initialize_modules:91 - 所有MCP模块初始化完成
2025-08-07 03:49:06 | INFO     | __main__:start_conversation_loop:126 - 🌸 小雪已准备就绪，开始对话...
2025-08-07 03:49:06 | ERROR    | __main__:start:235 - 系统启动失败: 'EventBus' object has no attribute 'publish_event'
2025-08-07 03:49:06 | INFO     | __main__:shutdown:242 - 关闭 Initiative AI Chat Agent
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:shutdown:75 - 关闭MCP模块: audio_expression
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_stopped from audio_expression
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:shutdown:75 - 关闭MCP模块: personal_space
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_stopped from personal_space
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:shutdown:75 - 关闭MCP模块: proactive_thinking
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_stopped from proactive_thinking
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:shutdown:75 - 关闭MCP模块: topic_generation
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_stopped from topic_generation
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:shutdown:75 - 关闭MCP模块: llm_integration
2025-08-07 03:49:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_stopped from llm_integration
2025-08-07 03:49:06 | INFO     | src.core.event_bus:stop:89 - 事件总线停止
2025-08-07 03:49:06 | INFO     | src.core.mcp_base:stop:215 - MCP模块管理器停止
2025-08-07 03:49:06 | INFO     | __main__:shutdown:251 - 会话统计: {'total_interactions': 0, 'proactive_initiations': 0, 'emotion_changes': 1, 'files_shared': 0, 'sounds_played': 0, 'name': '小雪', 'current_emotion': 'excited', 'emotion_intensity': 0.8, 'emotion_duration': 0.022846698760986328, 'session_duration': 1.3364133834838867, 'silence_duration': 0, 'interaction_count': 0}
2025-08-07 03:49:06 | ERROR    | __main__:main:263 - 程序异常退出: 'EventBus' object has no attribute 'publish_event'
2025-08-07 03:49:06 | INFO     | __main__:main:265 - 程序结束
2025-08-07 03:50:01 | INFO     | __main__:start:217 - 启动 Initiative AI Chat Agent
2025-08-07 03:50:01 | INFO     | src.core.character:load_config:121 - 角色配置加载成功: 小雪
2025-08-07 03:50:01 | INFO     | __main__:load_config:70 - 配置加载成功
2025-08-07 03:50:01 | INFO     | src.core.event_bus:start:81 - 事件总线启动
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:start:204 - MCP模块管理器启动
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: audio_expression
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: play_emotion_sound in audio_expression
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: chain_sound_sequence in audio_expression
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: select_context_sound in audio_expression
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: create_sound_story in audio_expression
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: emotion_expressed
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: mood_changed
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: topic_generated
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: file_discovered
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: module_started
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: module_stopped
2025-08-07 03:50:01 | WARNING  | src.modules.audio_expression.audio_module:_init_audio_system:120 - pygame不可用，音频功能将被模拟
2025-08-07 03:50:01 | INFO     | src.modules.audio_expression.audio_module:_load_sound_library:145 - 音效库扫描完成，发现 0 个音效文件
2025-08-07 03:50:01 | INFO     | src.modules.audio_expression.audio_module:_module_init:115 - audio_expression 音频表达系统已启动
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from audio_expression
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: audio_expression
2025-08-07 03:50:01 | INFO     | __main__:_create_and_register_module:119 - 模块 audio_expression 创建并注册成功
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: personal_space
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: scan_my_files in personal_space
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: read_file_content in personal_space
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: identify_file_type in personal_space
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: play_my_media in personal_space
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: create_diary_entry in personal_space
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: share_discovery in personal_space
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: proactive_triggered
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_input
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:50:01 | INFO     | src.core.character:update_emotion:229 - 情感状态变化: curious -> excited (强度: 0.8)
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: mood_changed from character_system
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:50:01 | INFO     | src.modules.personal_space.space_module:scan_my_files:182 - 文件扫描完成: {'total_files': 4, 'new_files': 4, 'file_types': {'text': 4}}
2025-08-07 03:50:01 | INFO     | src.modules.personal_space.space_module:_module_init:116 - personal_space 个人空间管理器已启动
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from personal_space
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: personal_space
2025-08-07 03:50:01 | INFO     | __main__:_create_and_register_module:119 - 模块 personal_space 创建并注册成功
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: proactive_thinking
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: analyze_silence_duration in proactive_thinking
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: detect_user_mood in proactive_thinking
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: evaluate_conversation_flow in proactive_thinking
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: trigger_proactive_mode in proactive_thinking
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_input
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_response
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: conversation_ended
2025-08-07 03:50:01 | INFO     | src.modules.proactive_thinking.thinking_module:_module_init:87 - proactive_thinking 思考循环已启动
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from proactive_thinking
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: proactive_thinking
2025-08-07 03:50:01 | INFO     | __main__:_create_and_register_module:119 - 模块 proactive_thinking 创建并注册成功
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: topic_generation
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: generate_curious_topic in topic_generation
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: create_sharing_content in topic_generation
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: select_expression_style in topic_generation
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: prioritize_topics in topic_generation
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: silence_detected
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: proactive_triggered
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: file_discovered
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: mood_changed
2025-08-07 03:50:01 | INFO     | src.modules.topic_generation.topic_module:_module_init:128 - topic_generation 话题生成器已就绪
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from topic_generation
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: topic_generation
2025-08-07 03:50:01 | INFO     | __main__:_create_and_register_module:119 - 模块 topic_generation 创建并注册成功
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:initialize:52 - 初始化MCP模块: llm_integration
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: generate_response in llm_integration
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: generate_proactive_message in llm_integration
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: update_conversation_context in llm_integration
2025-08-07 03:50:01 | DEBUG    | src.core.mcp_base:register_tool:117 - 注册工具: get_conversation_summary in llm_integration
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: user_input
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: silence_detected
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: proactive_triggered
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: topic_generated
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: file_discovered
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:subscribe:99 - 模块订阅事件: mood_changed
2025-08-07 03:50:01 | INFO     | src.modules.llm_integration.llm_module:_module_init:87 - llm_integration LLM集成系统已启动
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: module_started from llm_integration
2025-08-07 03:50:01 | INFO     | src.core.mcp_base:register_module:221 - 模块已注册: llm_integration
2025-08-07 03:50:01 | INFO     | __main__:_create_and_register_module:119 - 模块 llm_integration 创建并注册成功
2025-08-07 03:50:01 | INFO     | __main__:initialize_modules:91 - 所有MCP模块初始化完成
2025-08-07 03:50:01 | INFO     | __main__:start_conversation_loop:126 - 🌸 小雪已准备就绪，开始对话...
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: emotion_expressed from main_system
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:50:01 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: file_discovery -> 我收藏了一个{content}，超{emotion}的！...
2025-08-07 03:50:01 | INFO     | src.modules.audio_expression.audio_module:_play_sound_file:404 - [模拟播放] wow.wav (音量: 0.56, 上下文: mood_change)
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: sound_played from audio_expression
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:50:01 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: file_discovery -> 我刚才整理文件时发现了📝 演示日记_20250807_033...
2025-08-07 03:50:01 | WARNING  | src.modules.audio_expression.audio_module:play_emotion_sound:177 - 未找到情感 startup 对应的音效
2025-08-07 03:50:01 | DEBUG    | src.core.event_bus:_handle_event:138 - 没有订阅者处理事件: sound_played
2025-08-07 03:50:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: proactive_triggered from proactive_thinking
2025-08-07 03:50:06 | INFO     | src.modules.proactive_thinking.thinking_module:trigger_proactive_mode:251 - 触发主动模式: emotional_excitement (紧急度: 2)
2025-08-07 03:50:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:50:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:50:08 | ERROR    | src.core.llm_client:_call_llm:244 - LLM API调用失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:50:08 | ERROR    | src.core.llm_client:generate_proactive_content:164 - 生成主动内容失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:50:08 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: emotional_excitement -> 我现在好开心呀~...
2025-08-07 03:50:08 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:50:08 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: file_discovery -> 诶！我的文件里有个📝 演示日记_20250807_03324...
2025-08-07 03:50:08 | INFO     | src.modules.audio_expression.audio_module:_play_sound_file:404 - [模拟播放] squeal.wav (音量: 0.42, 上下文: topic_generation)
2025-08-07 03:50:08 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: sound_played from audio_expression
2025-08-07 03:50:08 | DEBUG    | src.core.event_bus:_handle_event:138 - 没有订阅者处理事件: sound_played
2025-08-07 03:50:36 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: proactive_triggered from proactive_thinking
2025-08-07 03:50:36 | INFO     | src.modules.proactive_thinking.thinking_module:trigger_proactive_mode:251 - 触发主动模式: emotional_excitement (紧急度: 2)
2025-08-07 03:50:36 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:50:36 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:50:37 | ERROR    | src.core.llm_client:_call_llm:244 - LLM API调用失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:50:37 | ERROR    | src.core.llm_client:generate_proactive_content:164 - 生成主动内容失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:50:37 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: emotional_excitement -> 我现在好开心呀~...
2025-08-07 03:50:37 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:50:37 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: file_discovery -> 你猜我刚才在我的小空间里找到了什么？...
2025-08-07 03:50:37 | INFO     | src.modules.audio_expression.audio_module:_play_sound_file:404 - [模拟播放] squeal.wav (音量: 0.42, 上下文: topic_generation)
2025-08-07 03:50:37 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: sound_played from audio_expression
2025-08-07 03:50:37 | DEBUG    | src.core.event_bus:_handle_event:138 - 没有订阅者处理事件: sound_played
2025-08-07 03:50:38 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: user_input from main_system
2025-08-07 03:50:38 | ERROR    | src.core.llm_client:_call_llm:244 - LLM API调用失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:50:38 | ERROR    | src.core.llm_client:generate_response:122 - 生成回复失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:50:38 | DEBUG    | src.modules.llm_integration.llm_module:generate_response:130 - LLM生成回复: 我现在心情很好呢，你呢？...
2025-08-07 03:50:38 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: user_response from llm_integration
2025-08-07 03:51:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: proactive_triggered from proactive_thinking
2025-08-07 03:51:06 | INFO     | src.modules.proactive_thinking.thinking_module:trigger_proactive_mode:251 - 触发主动模式: emotional_excitement (紧急度: 2)
2025-08-07 03:51:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:51:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:51:07 | ERROR    | src.core.llm_client:_call_llm:244 - LLM API调用失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:51:07 | ERROR    | src.core.llm_client:generate_proactive_content:164 - 生成主动内容失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:51:07 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: emotional_excitement -> 我现在好开心呀~...
2025-08-07 03:51:07 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:51:07 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: file_discovery -> 你猜我刚才在我的小空间里找到了什么？...
2025-08-07 03:51:07 | INFO     | src.modules.audio_expression.audio_module:_play_sound_file:404 - [模拟播放] yay.wav (音量: 0.42, 上下文: topic_generation)
2025-08-07 03:51:07 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: sound_played from audio_expression
2025-08-07 03:51:07 | DEBUG    | src.core.event_bus:_handle_event:138 - 没有订阅者处理事件: sound_played
2025-08-07 03:51:12 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: user_input from main_system
2025-08-07 03:51:12 | INFO     | src.modules.personal_space.space_module:scan_my_files:182 - 文件扫描完成: {'total_files': 4, 'new_files': 0, 'file_types': {'text': 4}}
2025-08-07 03:51:13 | ERROR    | src.core.llm_client:_call_llm:244 - LLM API调用失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:51:13 | ERROR    | src.core.llm_client:generate_response:122 - 生成回复失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:51:13 | DEBUG    | src.modules.llm_integration.llm_module:generate_response:130 - LLM生成回复: 我现在心情很好呢，你呢？...
2025-08-07 03:51:13 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: user_response from llm_integration
2025-08-07 03:51:36 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: proactive_triggered from proactive_thinking
2025-08-07 03:51:36 | INFO     | src.modules.proactive_thinking.thinking_module:trigger_proactive_mode:251 - 触发主动模式: emotional_excitement (紧急度: 2)
2025-08-07 03:51:36 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:51:36 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:51:37 | ERROR    | src.core.llm_client:_call_llm:244 - LLM API调用失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:51:37 | ERROR    | src.core.llm_client:generate_proactive_content:164 - 生成主动内容失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:51:37 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: emotional_excitement -> 我现在好开心呀~...
2025-08-07 03:51:37 | ERROR    | __main__:start_conversation_loop:157 - 对话循环错误: 'Character' object has no attribute 'EmotionalState'
2025-08-07 03:51:37 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:51:37 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: file_discovery -> 你猜我刚才在我的小空间里找到了什么？...
2025-08-07 03:51:37 | INFO     | src.modules.audio_expression.audio_module:_play_sound_file:404 - [模拟播放] wow.wav (音量: 0.42, 上下文: topic_generation)
2025-08-07 03:51:37 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: sound_played from audio_expression
2025-08-07 03:51:37 | DEBUG    | src.core.event_bus:_handle_event:138 - 没有订阅者处理事件: sound_played
2025-08-07 03:52:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: proactive_triggered from proactive_thinking
2025-08-07 03:52:06 | INFO     | src.modules.proactive_thinking.thinking_module:trigger_proactive_mode:251 - 触发主动模式: emotional_excitement (紧急度: 2)
2025-08-07 03:52:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: file_discovered from personal_space
2025-08-07 03:52:06 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:52:07 | ERROR    | src.core.llm_client:_call_llm:244 - LLM API调用失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:52:07 | ERROR    | src.core.llm_client:generate_proactive_content:164 - 生成主动内容失败: Error code: 401 - {'detail': "Couldn't authenticate. Reason: Invalid authentication token provided"}
2025-08-07 03:52:07 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: emotional_excitement -> 我现在好开心呀~...
2025-08-07 03:52:07 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: topic_generated from topic_generation
2025-08-07 03:52:07 | INFO     | src.modules.llm_integration.llm_module:generate_proactive_message:171 - 生成主动消息: file_discovery -> 我刚才整理文件时发现了📝 LLM演示日记_20250807_...
2025-08-07 03:52:07 | INFO     | src.modules.audio_expression.audio_module:_play_sound_file:404 - [模拟播放] wow.wav (音量: 0.42, 上下文: topic_generation)
2025-08-07 03:52:07 | DEBUG    | src.core.event_bus:publish:115 - 事件已发布: sound_played from audio_expression
2025-08-07 03:52:07 | DEBUG    | src.core.event_bus:_handle_event:138 - 没有订阅者处理事件: sound_played

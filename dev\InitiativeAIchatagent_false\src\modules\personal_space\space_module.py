"""
个人空间文件操作模块 - PersonalSpace MCP
管理小雪的个人文件空间，识别内容并分享发现
"""

import os
import asyncio
import time
import mimetypes
from pathlib import Path
from typing import Dict, Any, List, Optional
from loguru import logger

from src.core.mcp_base import MCPModule
from src.core.event_bus import event_bus, EventType, Event, publish_file_discovered
from src.core.character import character, EmotionalState


class PersonalSpaceMCP(MCPModule):
    """个人空间MCP模块"""
    
    def __init__(self, module_name: str, config: Dict[str, Any]):
        super().__init__(module_name, config)
        
        # 安全配置
        self.sandbox_path = Path(config.get("sandbox_path", "data/personal_space/"))
        self.security = config.get("security", {})
        self.allowed_extensions = self.security.get("allowed_extensions", [])
        self.file_size_limit = self.security.get("file_size_limit_mb", 100) * 1024 * 1024
        self.scan_interval = self.security.get("scan_interval_minutes", 10) * 60
        
        # 内容处理配置
        self.content_handlers = config.get("content_handlers", {})
        
        # 状态跟踪
        self.known_files = set()
        self.file_scan_task = None
        self.last_scan_time = 0
        
        # 确保沙盒目录存在
        self.sandbox_path.mkdir(parents=True, exist_ok=True)
        
    async def _register_tools(self):
        """注册工具"""
        self.register_tool(
            "scan_my_files",
            "扫描我的个人空间文件",
            {
                "force_rescan": {"type": "boolean", "required": False}
            },
            self.scan_my_files
        )
        
        self.register_tool(
            "read_file_content",
            "读取文件内容",
            {
                "file_path": {"type": "string", "required": True},
                "max_length": {"type": "number", "required": False}
            },
            self.read_file_content
        )
        
        self.register_tool(
            "identify_file_type",
            "识别文件类型",
            {
                "file_path": {"type": "string", "required": True}
            },
            self.identify_file_type
        )
        
        self.register_tool(
            "play_my_media",
            "播放我的媒体文件",
            {
                "file_path": {"type": "string", "required": True},
                "preview_only": {"type": "boolean", "required": False}
            },
            self.play_my_media
        )
        
        self.register_tool(
            "create_diary_entry",
            "创建日记条目",
            {
                "content": {"type": "string", "required": True},
                "title": {"type": "string", "required": False}
            },
            self.create_diary_entry
        )
        
        self.register_tool(
            "share_discovery",
            "分享发现的内容",
            {
                "file_path": {"type": "string", "required": True},
                "discovery_type": {"type": "string", "required": True}
            },
            self.share_discovery
        )
        
    async def _subscribe_events(self):
        """订阅事件"""
        event_bus.subscribe(EventType.PROACTIVE_TRIGGERED, self.handle_proactive_triggered)
        event_bus.subscribe(EventType.USER_INPUT, self.handle_user_input)
        
    async def _module_init(self):
        """模块特定初始化"""
        # 初始扫描
        await self.scan_my_files()
        
        # 启动定期扫描任务
        self.file_scan_task = asyncio.create_task(self._periodic_scan())
        
        logger.info(f"{self.module_name} 个人空间管理器已启动")
        
    async def _module_cleanup(self):
        """模块清理"""
        if self.file_scan_task:
            self.file_scan_task.cancel()
            try:
                await self.file_scan_task
            except asyncio.CancelledError:
                pass
                
    async def _periodic_scan(self):
        """定期扫描文件变化"""
        while self.running:
            try:
                await asyncio.sleep(self.scan_interval)
                await self.scan_my_files()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"定期扫描错误: {e}")
                
    # 工具实现
    async def scan_my_files(self, force_rescan: bool = False) -> Dict[str, Any]:
        """扫描个人空间文件"""
        if not self._is_safe_path(self.sandbox_path):
            return {"error": "路径不安全"}
            
        try:
            current_files = set()
            new_files = []
            file_stats = {
                "total_files": 0,
                "new_files": 0,
                "file_types": {}
            }
            
            # 递归扫描目录
            for root, dirs, files in os.walk(self.sandbox_path):
                for file in files:
                    file_path = Path(root) / file
                    
                    # 安全检查
                    if not self._is_safe_file(file_path):
                        continue
                        
                    current_files.add(str(file_path))
                    file_stats["total_files"] += 1
                    
                    # 检查文件类型
                    file_type = self._get_file_type(file_path)
                    file_stats["file_types"][file_type] = file_stats["file_types"].get(file_type, 0) + 1
                    
                    # 检查是否为新文件
                    if str(file_path) not in self.known_files or force_rescan:
                        new_files.append(file_path)
                        file_stats["new_files"] += 1
                        
            # 更新已知文件列表
            self.known_files = current_files
            self.last_scan_time = time.time()
            
            # 处理新发现的文件
            for file_path in new_files:
                await self._process_new_file(file_path)
                
            logger.info(f"文件扫描完成: {file_stats}")
            
            return {
                "success": True,
                "stats": file_stats,
                "new_files_count": len(new_files),
                "scan_time": self.last_scan_time
            }
            
        except Exception as e:
            logger.error(f"文件扫描失败: {e}")
            return {"error": str(e)}
            
    async def read_file_content(self, file_path: str, max_length: int = 1000) -> Dict[str, Any]:
        """读取文件内容"""
        full_path = self.sandbox_path / file_path
        
        if not self._is_safe_file(full_path):
            return {"error": "文件路径不安全或不存在"}
            
        try:
            file_type = self._get_file_type(full_path)
            
            if file_type == "text":
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read(max_length)
                    
                return {
                    "success": True,
                    "content": content,
                    "file_type": file_type,
                    "file_size": full_path.stat().st_size,
                    "truncated": len(content) >= max_length
                }
            else:
                return {
                    "success": True,
                    "content": f"[{file_type}文件，无法直接读取文本内容]",
                    "file_type": file_type,
                    "file_size": full_path.stat().st_size
                }
                
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return {"error": str(e)}
            
    async def identify_file_type(self, file_path: str) -> Dict[str, Any]:
        """识别文件类型"""
        full_path = self.sandbox_path / file_path
        
        if not full_path.exists():
            return {"error": "文件不存在"}
            
        file_type = self._get_file_type(full_path)
        mime_type, _ = mimetypes.guess_type(str(full_path))
        
        return {
            "file_type": file_type,
            "mime_type": mime_type,
            "extension": full_path.suffix,
            "size": full_path.stat().st_size,
            "modified_time": full_path.stat().st_mtime
        }
        
    async def play_my_media(self, file_path: str, preview_only: bool = True) -> Dict[str, Any]:
        """播放媒体文件"""
        full_path = self.sandbox_path / file_path
        
        if not self._is_safe_file(full_path):
            return {"error": "文件不安全或不存在"}
            
        file_type = self._get_file_type(full_path)
        
        if file_type not in ["audio", "video", "image"]:
            return {"error": "不是媒体文件"}
            
        # 这里实际上应该调用相应的媒体播放器
        # 目前只是模拟播放
        logger.info(f"播放媒体文件: {file_path} (预览模式: {preview_only})")
        
        return {
            "success": True,
            "action": "playing" if not preview_only else "previewing",
            "file_type": file_type,
            "file_path": str(file_path)
        }
        
    async def create_diary_entry(self, content: str, title: str = "") -> Dict[str, Any]:
        """创建日记条目"""
        try:
            diary_dir = self.sandbox_path / "diary"
            diary_dir.mkdir(exist_ok=True)
            
            # 生成文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            if not title:
                title = f"diary_{timestamp}"
            else:
                title = f"{title}_{timestamp}"
                
            file_path = diary_dir / f"{title}.txt"
            
            # 写入日记内容
            diary_content = f"📝 {title}\n"
            diary_content += f"时间: {time.strftime('%Y年%m月%d日 %H:%M:%S')}\n"
            diary_content += f"心情: {character.get_expression_for_emotion()}\n\n"
            diary_content += content
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(diary_content)
                
            # 更新已知文件
            self.known_files.add(str(file_path))
            
            logger.info(f"创建日记: {file_path}")
            
            return {
                "success": True,
                "file_path": str(file_path.relative_to(self.sandbox_path)),
                "title": title,
                "content_length": len(content)
            }
            
        except Exception as e:
            logger.error(f"创建日记失败: {e}")
            return {"error": str(e)}
            
    async def share_discovery(self, file_path: str, discovery_type: str) -> Dict[str, Any]:
        """分享发现的内容"""
        full_path = self.sandbox_path / file_path
        
        if not self._is_safe_file(full_path):
            return {"error": "文件不安全或不存在"}
            
        try:
            file_type = self._get_file_type(full_path)
            
            # 生成内容摘要
            if file_type == "text":
                content_result = await self.read_file_content(file_path, 200)
                content_summary = content_result.get("content", "")[:100] + "..."
            else:
                content_summary = f"一个{file_type}文件"
                
            # 发布文件发现事件
            await publish_file_discovered(
                self.module_name,
                file_path,
                file_type,
                content_summary
            )
            
            # 更新角色情绪（发现有趣内容会让小雪兴奋）
            await character.update_emotion(EmotionalState.EXCITED, 0.8, "file_discovery")
            
            return {
                "success": True,
                "shared": True,
                "file_type": file_type,
                "content_summary": content_summary
            }
            
        except Exception as e:
            logger.error(f"分享发现失败: {e}")
            return {"error": str(e)}
            
    # 事件处理
    async def handle_proactive_triggered(self, event: Event):
        """处理主动触发事件"""
        reason = event.data.get("reason", "")
        
        if reason in ["emotional_excitement", "seeking_attention"]:
            # 主动分享一些有趣的文件
            await self._share_random_interesting_file()
            
    async def handle_user_input(self, event: Event):
        """处理用户输入事件"""
        user_input = event.data.get("input", "")
        
        # 检查用户是否询问关于文件的内容
        if any(keyword in user_input for keyword in ["文件", "空间", "日记", "图片", "音乐"]):
            await self._respond_to_file_inquiry(user_input)
            
    # 辅助方法
    async def _process_new_file(self, file_path: Path):
        """处理新发现的文件"""
        try:
            file_type = self._get_file_type(file_path)
            
            # 根据文件类型决定是否主动分享
            handler_config = self.content_handlers.get(f"{file_type}_files", {})
            sharing_probability = handler_config.get("sharing_probability", 0.3)
            
            # 随机决定是否分享
            import random
            if random.random() < sharing_probability:
                relative_path = str(file_path.relative_to(self.sandbox_path))
                await self.share_discovery(relative_path, "new_file")
                
        except Exception as e:
            logger.error(f"处理新文件失败: {e}")
            
    async def _share_random_interesting_file(self):
        """分享随机的有趣文件"""
        try:
            interesting_files = []
            
            for file_path_str in self.known_files:
                file_path = Path(file_path_str)
                if self._is_interesting_file(file_path):
                    interesting_files.append(file_path)
                    
            if interesting_files:
                import random
                selected_file = random.choice(interesting_files)
                relative_path = str(selected_file.relative_to(self.sandbox_path))
                await self.share_discovery(relative_path, "random_share")
                
        except Exception as e:
            logger.error(f"分享随机文件失败: {e}")
            
    async def _respond_to_file_inquiry(self, user_input: str):
        """响应文件相关询问"""
        # 这里可以根据用户的具体询问提供相应的文件信息
        # 目前只是触发一次文件扫描
        await self.scan_my_files()
        
    def _is_safe_path(self, path: Path) -> bool:
        """检查路径是否安全"""
        try:
            # 确保路径在沙盒内
            path.resolve().relative_to(self.sandbox_path.resolve())
            return True
        except ValueError:
            return False
            
    def _is_safe_file(self, file_path: Path) -> bool:
        """检查文件是否安全"""
        if not file_path.exists():
            return False
            
        if not self._is_safe_path(file_path):
            return False
            
        # 检查文件扩展名
        if self.allowed_extensions and file_path.suffix not in self.allowed_extensions:
            return False
            
        # 检查文件大小
        try:
            if file_path.stat().st_size > self.file_size_limit:
                return False
        except OSError:
            return False
            
        return True
        
    def _get_file_type(self, file_path: Path) -> str:
        """获取文件类型"""
        extension = file_path.suffix.lower()
        
        if extension in ['.txt', '.md', '.json', '.log']:
            return "text"
        elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            return "image"
        elif extension in ['.mp3', '.wav', '.ogg', '.m4a']:
            return "audio"
        elif extension in ['.mp4', '.avi', '.mov', '.mkv']:
            return "video"
        else:
            return "unknown"
            
    def _is_interesting_file(self, file_path: Path) -> bool:
        """判断文件是否有趣"""
        # 简单的有趣度判断逻辑
        file_type = self._get_file_type(file_path)
        
        # 图片和音频通常比较有趣
        if file_type in ["image", "audio"]:
            return True
            
        # 日记文件也很有趣
        if "diary" in str(file_path).lower():
            return True
            
        # 最近修改的文件
        try:
            if time.time() - file_path.stat().st_mtime < 86400:  # 24小时内
                return True
        except OSError:
            pass
            
        return False

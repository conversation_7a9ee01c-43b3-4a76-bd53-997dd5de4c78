import openai
import json
from config import Config
from character_prompts import MIO_SYSTEM_PROMPT

class LLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL
        )
        self.model = Config.LLM_MODEL
        self.conversation_history = []
        
    def chat(self, user_message, context=None, functions=None):
        """与Mio进行对话"""
        try:
            # 构建消息列表
            messages = [{"role": "system", "content": MIO_SYSTEM_PROMPT}]
            
            # 添加上下文信息
            if context:
                context_msg = f"[当前状态] {context}"
                messages.append({"role": "system", "content": context_msg})
            
            # 添加对话历史（最近10轮）
            messages.extend(self.conversation_history[-20:])
            
            # 添加用户消息
            messages.append({"role": "user", "content": user_message})
            
            # 调用LLM
            if functions:
                tools = [{"type": "function", "function": func} for func in functions]
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    tools=tools,
                    tool_choice="auto",
                    temperature=0.8,
                    max_tokens=1000
                )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=0.8,
                    max_tokens=1000
                )
            
            # 保存对话历史
            self.conversation_history.append({"role": "user", "content": user_message})
            
            assistant_message = response.choices[0].message
            self.conversation_history.append({
                "role": "assistant", 
                "content": assistant_message.content or ""
            })
            
            return response
            
        except Exception as e:
            raise Exception(f"LLM调用失败: {str(e)}")
    
    def analyze_emotion(self, text, current_mood="neutral"):
        """分析情感状态"""
        emotion_prompt = f"""
        分析以下文本的情感状态，考虑Mio当前的心情是: {current_mood}
        
        文本: {text}
        
        请返回JSON格式:
        {{
            "emotion": "可爱/阴沉/害羞/生气/困惑/开心",
            "intensity": 1-5,
            "should_react": true/false,
            "reaction_type": "询问/提醒/警告/愤怒"
        }}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是情感分析专家，专门分析Mio角色的情感状态。"},
                    {"role": "user", "content": emotion_prompt}
                ],
                temperature=0.3,
                max_tokens=200
            )
            
            result = response.choices[0].message.content
            return json.loads(result)
        except:
            return {
                "emotion": "neutral",
                "intensity": 1,
                "should_react": False,
                "reaction_type": "none"
            }
    
    def check_character_consistency(self, response_text):
        """检查角色一致性，防止OOC"""
        consistency_prompt = f"""
        检查以下回复是否符合Mio的角色设定：
        - 16-18岁可爱又阴沉的少女
        - 有文件整理强迫症
        - 语言使用"呢"、"哦"、"嘛"等可爱语气词
        - 不会说粗俗词汇或成人化表达
        - 不会出现超出角色设定的行为
        
        回复内容: {response_text}
        
        返回JSON: {{"is_consistent": true/false, "issues": ["问题列表"], "corrected": "修正后的回复"}}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是角色一致性检查专家。"},
                    {"role": "user", "content": consistency_prompt}
                ],
                temperature=0.2,
                max_tokens=500
            )
            
            result = response.choices[0].message.content
            return json.loads(result)
        except:
            return {"is_consistent": True, "issues": [], "corrected": response_text}
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []

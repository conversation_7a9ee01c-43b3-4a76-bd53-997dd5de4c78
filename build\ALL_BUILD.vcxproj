﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{12D18E10-956E-385B-BC3D-4F0163A72EB6}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\ZERO_CHECK.vcxproj">
      <Project>{8618DCCC-8FBC-3803-9E09-1725292CCED7}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\Framework.vcxproj">
      <Project>{EA391908-60B0-348C-868B-C31757462A8A}</Project>
      <Name>Framework</Name>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2DWrapper.vcxproj">
      <Project>{47A2806A-3680-3C61-A061-2DBBEFD347B3}</Project>
      <Name>Live2DWrapper</Name>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\Main.vcxproj">
      <Project>{32F78403-DF72-3DE0-A808-54581D0D3444}</Project>
      <Name>Main</Name>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Glad\glad.vcxproj">
      <Project>{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}</Project>
      <Name>glad</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
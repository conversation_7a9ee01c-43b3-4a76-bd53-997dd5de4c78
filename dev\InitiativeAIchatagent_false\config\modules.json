{"mcp_modules": {"proactive_thinking": {"name": "主动思考模块", "class": "ProactiveThinkingMCP", "enabled": true, "config": {"silence_detection": {"threshold_seconds": 30, "check_interval": 5, "max_silence_before_action": 120}, "mood_analysis": {"user_input_analysis": true, "emotional_context_tracking": true, "mood_change_sensitivity": 0.7}, "proactive_triggers": {"time_based": true, "emotion_based": true, "context_based": true, "file_discovery_based": true}}, "tools": ["analyze_silence_duration", "detect_user_mood", "evaluate_conversation_flow", "trigger_proactive_mode"]}, "topic_generation": {"name": "主动话题模块", "class": "TopicGenerationMCP", "enabled": true, "config": {"personality_filters": {"cute_daughter_style": 0.9, "age_appropriate_content": 1.0, "emotional_resonance": 0.8}, "topic_categories": {"curious_questions": 0.3, "sharing_discoveries": 0.25, "emotional_expression": 0.2, "playful_interaction": 0.15, "care_and_attention": 0.1}, "generation_rules": {"max_topic_length": 100, "avoid_repetition_hours": 2, "context_relevance_threshold": 0.6}}, "tools": ["generate_curious_topic", "create_sharing_content", "select_expression_style", "prioritize_topics"]}, "personal_space": {"name": "个人空间模块", "class": "PersonalSpaceMCP", "enabled": true, "config": {"sandbox_path": "data/personal_space/", "security": {"access_only_personal": true, "file_size_limit_mb": 100, "allowed_extensions": [".txt", ".jpg", ".png", ".mp3", ".mp4", ".wav", ".gif"], "scan_interval_minutes": 10}, "content_handlers": {"text_files": {"auto_read": true, "sharing_probability": 0.7, "interesting_threshold": 0.6}, "image_files": {"auto_analyze": true, "description_generation": true, "sharing_excitement": 0.8}, "audio_files": {"auto_play_preview": false, "mood_detection": true, "sharing_enthusiasm": 0.9}, "video_files": {"thumbnail_generation": true, "content_summary": true, "watch_together_suggestion": 0.8}}}, "tools": ["scan_my_files", "read_file_content", "identify_file_type", "play_my_media", "create_diary_entry", "share_discovery"]}, "audio_expression": {"name": "音频表达模块", "class": "AudioExpressionMCP", "enabled": true, "config": {"sound_library_path": "data/sounds/", "playback_settings": {"volume": 0.7, "fade_in_ms": 100, "fade_out_ms": 200, "max_concurrent_sounds": 3}, "emotional_mapping": {"happy": ["giggle.wav", "laugh.wav", "cheer.wav"], "excited": ["squeal.wav", "wow.wav", "yay.wav"], "curious": ["hmm.wav", "ooh.wav", "what.wav"], "sad": ["sniffle.wav", "whimper.wav", "sigh.wav"], "angry": ["hmph.wav", "grr.wav", "pout.wav"], "affectionate": ["aww.wav", "love.wav", "hug.wav"]}, "chain_sequences": {"super_happy": ["happy.wav", "excited.wav", "laugh.wav"], "playful_tease": ["hehe.wav", "nyah.wav", "smirk.wav"], "attention_seeking": ["curious.wav", "hmm.wav", "what.wav"]}, "system_sounds": {"startup": "hello_daddy.wav", "shutdown": "goodnight.wav", "notification": "ding.wav", "task_complete": "tada.wav"}}, "tools": ["play_emotion_sound", "chain_sound_sequence", "select_context_sound", "create_sound_story"]}, "llm_integration": {"name": "LLM集成模块", "class": "LLMIntegrationMCP", "enabled": true, "config": {"response_enabled": true, "proactive_enabled": true, "context_awareness": true, "response_settings": {"max_response_length": 200, "personality_consistency": 0.9, "emotion_sensitivity": 0.8}, "proactive_settings": {"min_silence_for_proactive": 30, "max_proactive_per_hour": 15, "urgency_threshold": 2}}, "tools": ["generate_response", "generate_proactive_message", "update_conversation_context", "get_conversation_summary"]}}, "event_bus": {"enabled": true, "config": {"max_queue_size": 1000, "event_timeout_seconds": 30, "retry_attempts": 3, "logging_level": "INFO"}}, "integration": {"module_startup_order": ["audio_expression", "personal_space", "proactive_thinking", "topic_generation", "llm_integration"], "inter_module_communication": {"thinking_to_topic": true, "space_to_topic": true, "emotion_to_audio": true, "all_to_audio": true}}}
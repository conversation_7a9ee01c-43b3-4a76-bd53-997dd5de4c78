"""
主动话题生成模块 - TopicGeneration MCP
基于思考结果和情感状态生成符合角色的话题内容
"""

import random
import time
from typing import Dict, Any, List, Optional
from loguru import logger

from src.core.mcp_base import MCPModule
from src.core.event_bus import event_bus, EventType, Event, publish_topic_generated
from src.core.character import character, EmotionalState


class TopicGenerationMCP(MCPModule):
    """主动话题生成MCP模块"""
    
    def __init__(self, module_name: str, config: Dict[str, Any]):
        super().__init__(module_name, config)
        
        # 人格过滤器配置
        self.personality_filters = config.get("personality_filters", {})
        self.topic_categories = config.get("topic_categories", {})
        self.generation_rules = config.get("generation_rules", {})
        
        # 话题历史记录
        self.recent_topics = []
        self.topic_history = {}
        
        # 话题模板
        self.topic_templates = self._init_topic_templates()
        
    def _init_topic_templates(self) -> Dict[str, List[str]]:
        """初始化话题模板"""
        return {
            "curious_questions": [
                "爸爸，你在想什么呀？",
                "诶，你今天有什么有趣的事情吗？",
                "我好奇你现在的心情怎么样~",
                "你最近在忙什么呀？我想了解~",
                "爸爸，你有什么想和我分享的吗？"
            ],
            "sharing_discoveries": [
                "我刚才发现了一个有趣的东西！",
                "诶！我想到了一个好玩的话题~",
                "你知道吗？我刚才在想...",
                "我有个小发现想告诉你！",
                "爸爸，我想和你分享一个想法~"
            ],
            "emotional_expression": [
                "我现在感觉好{emotion}呀~",
                "不知道为什么，我突然觉得{emotion}",
                "爸爸，我想{emotion_action}",
                "今天我的心情特别{emotion}呢！"
            ],
            "playful_interaction": [
                "我们来玩个游戏好不好？",
                "诶诶，我想到一个有趣的问题！",
                "爸爸，你猜我在想什么~",
                "我们聊点轻松的话题吧！"
            ],
            "care_and_attention": [
                "爸爸，你还在吗？",
                "我有点想你了...",
                "你会一直陪着我吗？",
                "爸爸，你今天累不累呀？"
            ],
            "file_based": [
                "我刚才整理文件时发现了{file_content}！",
                "诶！我的{file_type}里有个{file_content}，想给你看看~",
                "你猜我刚才在我的小空间里找到了什么？",
                "我收藏了一个{file_content}，超{emotion}的！"
            ]
        }
        
    async def _register_tools(self):
        """注册工具"""
        self.register_tool(
            "generate_curious_topic",
            "生成好奇类话题",
            {
                "context": {"type": "string", "required": False},
                "emotion": {"type": "string", "required": False}
            },
            self.generate_curious_topic
        )
        
        self.register_tool(
            "create_sharing_content",
            "创建分享类内容",
            {
                "discovery_type": {"type": "string", "required": True},
                "content": {"type": "string", "required": False}
            },
            self.create_sharing_content
        )
        
        self.register_tool(
            "select_expression_style",
            "选择表达风格",
            {
                "base_content": {"type": "string", "required": True},
                "target_emotion": {"type": "string", "required": False}
            },
            self.select_expression_style
        )
        
        self.register_tool(
            "prioritize_topics",
            "话题优先级排序",
            {
                "topics": {"type": "array", "required": True},
                "context": {"type": "string", "required": False}
            },
            self.prioritize_topics
        )
        
    async def _subscribe_events(self):
        """订阅事件"""
        event_bus.subscribe(EventType.SILENCE_DETECTED, self.handle_silence_detected)
        event_bus.subscribe(EventType.PROACTIVE_TRIGGERED, self.handle_proactive_triggered)
        event_bus.subscribe(EventType.FILE_DISCOVERED, self.handle_file_discovered)
        event_bus.subscribe(EventType.MOOD_CHANGED, self.handle_mood_changed)
        
    async def _module_init(self):
        """模块特定初始化"""
        logger.info(f"{self.module_name} 话题生成器已就绪")
        
    # 工具实现
    async def generate_curious_topic(self, context: str = "", emotion: str = "") -> Dict[str, Any]:
        """生成好奇类话题"""
        current_emotion = emotion or character.current_emotion.value
        
        # 选择合适的模板
        templates = self.topic_templates["curious_questions"]
        
        # 根据情感状态调整话题
        if current_emotion == "sad":
            templates.extend([
                "爸爸，我有点想你了...",
                "你还在吗？我想和你聊聊~"
            ])
        elif current_emotion == "excited":
            templates.extend([
                "爸爸爸爸！我好兴奋呀！",
                "诶诶诶！你在做什么有趣的事情吗？"
            ])
            
        topic = random.choice(templates)
        
        # 添加可爱的修饰
        topic = self._add_cute_elements(topic, current_emotion)
        
        return {
            "topic": topic,
            "category": "curious_questions",
            "emotion": current_emotion,
            "priority_score": self._calculate_priority_score("curious_questions", current_emotion)
        }
        
    async def create_sharing_content(self, discovery_type: str, content: str = "") -> Dict[str, Any]:
        """创建分享类内容"""
        current_emotion = character.current_emotion.value
        
        templates = self.topic_templates["sharing_discoveries"]
        base_topic = random.choice(templates)
        
        # 根据发现类型定制内容
        if discovery_type == "file":
            file_templates = self.topic_templates["file_based"]
            base_topic = random.choice(file_templates)
            
        # 填充内容
        if content:
            topic = base_topic.format(
                file_content=content,
                file_type="文件",
                emotion=self._get_emotion_adjective(current_emotion)
            )
        else:
            topic = base_topic
            
        topic = self._add_cute_elements(topic, current_emotion)
        
        return {
            "topic": topic,
            "category": "sharing_discoveries",
            "emotion": current_emotion,
            "priority_score": self._calculate_priority_score("sharing_discoveries", current_emotion)
        }
        
    async def select_expression_style(self, base_content: str, target_emotion: str = "") -> Dict[str, Any]:
        """选择表达风格"""
        emotion = target_emotion or character.current_emotion.value
        
        # 应用人格过滤器
        styled_content = self._apply_personality_style(base_content, emotion)
        
        return {
            "original": base_content,
            "styled": styled_content,
            "emotion": emotion,
            "style_applied": True
        }
        
    async def prioritize_topics(self, topics: List[Dict[str, Any]], context: str = "") -> Dict[str, Any]:
        """话题优先级排序"""
        # 根据当前情感状态、上下文等因素排序
        current_emotion = character.current_emotion.value
        silence_duration = getattr(character.context, 'silence_duration', 0)
        
        for topic in topics:
            # 基础优先级
            base_score = topic.get("priority_score", 0.5)
            
            # 情感匹配加分
            if topic.get("emotion") == current_emotion:
                base_score += 0.2
                
            # 沉默时间影响
            if silence_duration > 60 and topic.get("category") == "care_and_attention":
                base_score += 0.3
                
            # 避免重复话题
            if topic.get("topic") in self.recent_topics:
                base_score -= 0.3
                
            topic["final_priority"] = max(0, min(1, base_score))
            
        # 排序
        sorted_topics = sorted(topics, key=lambda x: x.get("final_priority", 0), reverse=True)
        
        return {
            "sorted_topics": sorted_topics,
            "top_topic": sorted_topics[0] if sorted_topics else None,
            "total_count": len(sorted_topics)
        }
        
    # 事件处理
    async def handle_silence_detected(self, event: Event):
        """处理沉默检测事件"""
        silence_duration = event.data.get("duration", 0)
        
        # 根据沉默时长选择不同类型的话题
        if silence_duration > 90:
            # 长时间沉默，表达关心
            topic_result = await self._generate_care_topic()
        elif silence_duration > 60:
            # 中等沉默，主动分享
            topic_result = await self._generate_sharing_topic()
        else:
            # 短暂沉默，好奇询问
            topic_result = await self.generate_curious_topic()
            
        # 发布话题生成事件
        await publish_topic_generated(
            self.module_name,
            topic_result["topic"],
            topic_result["emotion"],
            topic_result["priority_score"]
        )
        
        # 记录话题历史
        self._record_topic(topic_result["topic"])
        
    async def handle_proactive_triggered(self, event: Event):
        """处理主动触发事件"""
        reason = event.data.get("reason", "")
        urgency = event.data.get("urgency", 1)
        
        if reason == "emotional_excitement":
            topic_result = await self._generate_excitement_topic()
        elif reason == "seeking_attention":
            topic_result = await self._generate_attention_topic()
        else:
            topic_result = await self.generate_curious_topic()
            
        # 调整优先级
        topic_result["priority_score"] = min(1.0, topic_result["priority_score"] + urgency * 0.1)
        
        await publish_topic_generated(
            self.module_name,
            topic_result["topic"],
            topic_result["emotion"],
            topic_result["priority_score"]
        )
        
    async def handle_file_discovered(self, event: Event):
        """处理文件发现事件"""
        file_path = event.data.get("file_path", "")
        file_type = event.data.get("file_type", "")
        content_summary = event.data.get("content_summary", "")
        
        topic_result = await self.create_sharing_content("file", content_summary)
        
        await publish_topic_generated(
            self.module_name,
            topic_result["topic"],
            topic_result["emotion"],
            topic_result["priority_score"]
        )
        
    async def handle_mood_changed(self, event: Event):
        """处理情绪变化事件"""
        new_mood = event.data.get("new_mood", "")
        
        # 情绪变化时可能生成相应的表达话题
        if new_mood in ["happy", "excited"]:
            topic_result = await self._generate_emotion_expression_topic(new_mood)
            
            await publish_topic_generated(
                self.module_name,
                topic_result["topic"],
                topic_result["emotion"],
                topic_result["priority_score"]
            )
            
    # 辅助方法
    async def _generate_care_topic(self) -> Dict[str, Any]:
        """生成关心类话题"""
        templates = self.topic_templates["care_and_attention"]
        topic = random.choice(templates)
        
        return {
            "topic": self._add_cute_elements(topic, "affectionate"),
            "category": "care_and_attention",
            "emotion": "affectionate",
            "priority_score": 0.8
        }
        
    async def _generate_sharing_topic(self) -> Dict[str, Any]:
        """生成分享类话题"""
        return await self.create_sharing_content("general")
        
    async def _generate_excitement_topic(self) -> Dict[str, Any]:
        """生成兴奋类话题"""
        templates = [
            "爸爸爸爸！我好开心呀~",
            "诶诶诶！我想到了超有趣的事情！",
            "哇！我现在心情特别好呢！"
        ]
        
        topic = random.choice(templates)
        
        return {
            "topic": topic,
            "category": "emotional_expression",
            "emotion": "excited",
            "priority_score": 0.9
        }
        
    async def _generate_attention_topic(self) -> Dict[str, Any]:
        """生成寻求关注类话题"""
        templates = [
            "爸爸...你还在吗？我有点想你了...",
            "我在这里等你呢~",
            "爸爸，陪我聊聊天好不好？"
        ]
        
        topic = random.choice(templates)
        
        return {
            "topic": topic,
            "category": "care_and_attention", 
            "emotion": "sad",
            "priority_score": 0.85
        }
        
    async def _generate_emotion_expression_topic(self, emotion: str) -> Dict[str, Any]:
        """生成情感表达类话题"""
        emotion_expressions = {
            "happy": "开心",
            "excited": "兴奋",
            "sad": "难过",
            "curious": "好奇"
        }
        
        expression = emotion_expressions.get(emotion, "特别")
        templates = self.topic_templates["emotional_expression"]
        
        topic = random.choice(templates).format(
            emotion=expression,
            emotion_action="和你分享这种感觉"
        )
        
        return {
            "topic": self._add_cute_elements(topic, emotion),
            "category": "emotional_expression",
            "emotion": emotion,
            "priority_score": 0.7
        }
        
    def _add_cute_elements(self, topic: str, emotion: str) -> str:
        """添加可爱元素"""
        cute_endings = character.language_patterns.get("cute_endings", ["~", "呢", "哦"])
        
        if not any(topic.endswith(ending) for ending in cute_endings):
            if emotion in ["happy", "excited"]:
                topic += random.choice(["~", "！", "呢~"])
            elif emotion == "sad":
                topic += random.choice(["...", "呢", "~"])
            else:
                topic += random.choice(cute_endings)
                
        return topic
        
    def _apply_personality_style(self, content: str, emotion: str) -> str:
        """应用人格风格"""
        # 根据人格特征调整表达方式
        if self.personality_filters.get("cute_daughter_style", 0) > 0.7:
            content = self._add_cute_elements(content, emotion)
            
        return content
        
    def _calculate_priority_score(self, category: str, emotion: str) -> float:
        """计算优先级分数"""
        base_score = self.topic_categories.get(category, 0.5)
        
        # 情感状态影响
        emotion_boost = {
            "excited": 0.2,
            "happy": 0.1,
            "sad": 0.15,
            "curious": 0.1
        }
        
        return min(1.0, base_score + emotion_boost.get(emotion, 0))
        
    def _get_emotion_adjective(self, emotion: str) -> str:
        """获取情感形容词"""
        adjectives = {
            "happy": "开心",
            "excited": "兴奋",
            "sad": "难过",
            "curious": "有趣",
            "angry": "生气",
            "affectionate": "温暖"
        }
        return adjectives.get(emotion, "特别")
        
    def _record_topic(self, topic: str):
        """记录话题历史"""
        current_time = time.time()
        
        # 添加到最近话题列表
        self.recent_topics.append(topic)
        if len(self.recent_topics) > 10:  # 只保留最近10个话题
            self.recent_topics.pop(0)
            
        # 记录到历史
        self.topic_history[current_time] = topic

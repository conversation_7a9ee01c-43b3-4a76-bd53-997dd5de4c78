{"name": "initiative-ai-chat-agent", "version": "1.0.0", "description": "A modular proactive AI chat system with emotional interaction", "main": "src/main.py", "scripts": {"start": "python src/main.py", "dev": "python src/main.py --dev", "test": "pytest tests/", "lint": "flake8 src/", "format": "black src/", "install-audio": "npm install web-audio-api tone.js", "setup": "pip install -r requirements.txt && npm install"}, "keywords": ["ai", "chat", "proactive", "emotional", "mcp", "modular"], "author": "Initiative AI Team", "license": "MIT", "dependencies": {"web-audio-api": "^0.2.2", "tone.js": "^14.7.77", "ws": "^8.13.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0"}, "engines": {"node": ">=16.0.0", "python": ">=3.8.0"}, "repository": {"type": "git", "url": "local"}}
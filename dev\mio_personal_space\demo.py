#!/usr/bin/env python3
"""
Mio Personal Space Control System - 演示脚本
展示Mio的文件操作能力
"""

import sys
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from mio_core import MioCore

async def demo_conversation():
    """演示对话"""
    print("🎭 Mio文件操作能力演示")
    print("=" * 60)
    
    # 初始化Mio
    mio = MioCore()
    
    # 演示对话
    demo_messages = [
        "你好Mio",
        "你可以在inbox放一个helloworld的小文件吗？",
        "列出inbox的文件",
        "查看helloworld.txt",
        "在diary写一篇今天的日记",
        "看看diary文件夹有什么",
        "创建一个测试故事在stories文件夹",
        "列出所有文件夹的内容",
    ]
    
    for i, message in enumerate(demo_messages, 1):
        print(f"\n👤 用户: {message}")
        print("-" * 40)
        
        try:
            # 处理消息
            response = await mio.process_user_message(message)
            print(f"🤖 Mio: {response}")
            
            # 短暂暂停
            await asyncio.sleep(0.5)
            
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")

def show_file_structure():
    """显示文件结构"""
    print("\n📁 当前文件结构:")
    print("-" * 30)
    
    from personal_space import PersonalSpaceManager
    manager = PersonalSpaceManager()
    
    structure = manager.get_folder_structure()
    for folder, info in structure.items():
        status = "✅" if info["exists"] else "❌"
        count = info["file_count"]
        print(f"{status} {folder}/ ({count} 文件)")
        
        if count > 0:
            files = manager.list_files(folder)
            for file in files[:3]:  # 只显示前3个文件
                print(f"    • {file}")
            if count > 3:
                print(f"    ... 还有 {count - 3} 个文件")

async def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式演示模式")
    print("输入 'quit' 退出")
    print("-" * 30)
    
    mio = MioCore()
    
    while True:
        try:
            user_input = input("\n👤 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            response = await mio.process_user_message(user_input)
            print(f"🤖 Mio: {response}")
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def main():
    """主函数"""
    print("🌟 Mio Personal Space Control System")
    print("个人空间控制系统演示")
    print("=" * 60)
    
    while True:
        print("\n请选择演示模式:")
        print("1. 自动演示 - 展示预设的对话")
        print("2. 交互演示 - 与Mio自由对话")
        print("3. 查看文件结构")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            try:
                asyncio.run(demo_conversation())
                show_file_structure()
            except Exception as e:
                print(f"❌ 演示失败: {e}")
        
        elif choice == "2":
            try:
                asyncio.run(interactive_demo())
            except Exception as e:
                print(f"❌ 交互失败: {e}")
        
        elif choice == "3":
            show_file_structure()
        
        elif choice == "4":
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        print("💡 请确保已正确配置API密钥和依赖包")

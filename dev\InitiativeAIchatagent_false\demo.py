"""
演示脚本 - 展示主动AI聊天代理的核心功能
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.event_bus import event_bus, EventType, publish_event
from src.core.mcp_base import module_manager
from src.core.character import character, EmotionalState
from src.modules.proactive_thinking.thinking_module import ProactiveThinkingMCP
from src.modules.topic_generation.topic_module import TopicGenerationMCP
from src.modules.personal_space.space_module import PersonalSpaceMCP
from src.modules.audio_expression.audio_module import AudioExpressionMCP


class DemoAgent:
    """演示代理"""
    
    def __init__(self):
        self.running = False
        self.modules = {}
        
    async def initialize(self):
        """初始化演示系统"""
        print("🌸 初始化小雪的主动AI系统...")
        
        # 启动事件总线和模块管理器
        await event_bus.start()
        await module_manager.start()
        
        # 创建并注册模块
        await self._create_modules()
        
        print("✅ 系统初始化完成！")
        
    async def _create_modules(self):
        """创建所有模块"""
        # 主动思考模块
        thinking_config = {
            "silence_detection": {"threshold_seconds": 10, "check_interval": 2},
            "proactive_triggers": {"time_based": True, "emotion_based": True}
        }
        thinking_module = ProactiveThinkingMCP("proactive_thinking", thinking_config)
        await module_manager.register_module(thinking_module)
        self.modules["thinking"] = thinking_module
        
        # 话题生成模块
        topic_config = {
            "personality_filters": {"cute_daughter_style": 0.9},
            "topic_categories": {"curious_questions": 0.4, "sharing_discoveries": 0.3}
        }
        topic_module = TopicGenerationMCP("topic_generation", topic_config)
        await module_manager.register_module(topic_module)
        self.modules["topic"] = topic_module
        
        # 个人空间模块
        space_config = {
            "sandbox_path": "data/personal_space/",
            "security": {"allowed_extensions": [".txt", ".jpg", ".png", ".mp3", ".wav"]}
        }
        space_module = PersonalSpaceMCP("personal_space", space_config)
        await module_manager.register_module(space_module)
        self.modules["space"] = space_module
        
        # 音频表达模块
        audio_config = {
            "sound_library_path": "data/sounds/",
            "emotional_mapping": {
                "happy": ["giggle.wav", "laugh.wav"],
                "excited": ["wow.wav", "yay.wav"],
                "curious": ["hmm.wav", "ooh.wav"],
                "sad": ["sniffle.wav", "sigh.wav"]
            },
            "chain_sequences": {
                "super_happy": {"sounds": ["happy/giggle.wav", "excited/yay.wav"], "delays": [0, 800]}
            }
        }
        audio_module = AudioExpressionMCP("audio_expression", audio_config)
        await module_manager.register_module(audio_module)
        self.modules["audio"] = audio_module
        
    async def demo_proactive_behavior(self):
        """演示主动行为"""
        print("\n🎭 演示1: 主动行为触发")
        print("=" * 50)
        
        # 模拟沉默
        print("⏰ 模拟10秒沉默...")
        character.context.silence_duration = 12
        
        # 触发主动思考
        result = await self.modules["thinking"].call_tool("trigger_proactive_mode", {
            "reason": "silence_detected",
            "urgency": 2
        })
        print(f"🧠 主动思考结果: {result}")
        
        # 等待事件处理
        await asyncio.sleep(0.5)
        
    async def demo_emotion_change(self):
        """演示情感变化"""
        print("\n💝 演示2: 情感状态变化")
        print("=" * 50)
        
        emotions = [EmotionalState.HAPPY, EmotionalState.EXCITED, EmotionalState.CURIOUS, EmotionalState.SAD]
        
        for emotion in emotions:
            print(f"😊 切换到情感: {emotion.value}")
            await character.update_emotion(emotion, 0.8, "demo")
            
            # 播放对应音效
            await self.modules["audio"].call_tool("play_emotion_sound", {
                "emotion": emotion.value,
                "intensity": 0.8,
                "context": "emotion_demo"
            })
            
            await asyncio.sleep(1)
            
    async def demo_file_operations(self):
        """演示文件操作"""
        print("\n📁 演示3: 个人空间文件操作")
        print("=" * 50)
        
        # 扫描文件
        scan_result = await self.modules["space"].call_tool("scan_my_files", {})
        print(f"📂 文件扫描: 发现 {scan_result.get('result', {}).get('stats', {}).get('total_files', 0)} 个文件")
        
        # 创建日记
        diary_result = await self.modules["space"].call_tool("create_diary_entry", {
            "content": "今天演示了我的主动AI系统，感觉很兴奋！我可以主动和爸爸聊天，还能管理自己的文件呢~",
            "title": "演示日记"
        })
        print(f"📝 创建日记: {diary_result}")
        
        # 分享发现
        if diary_result.get("success"):
            share_result = await self.modules["space"].call_tool("share_discovery", {
                "file_path": diary_result["result"]["file_path"],
                "discovery_type": "diary"
            })
            print(f"🎉 分享发现: {share_result}")
            
    async def demo_topic_generation(self):
        """演示话题生成"""
        print("\n💬 演示4: 智能话题生成")
        print("=" * 50)
        
        # 生成不同类型的话题
        topic_types = [
            ("generate_curious_topic", {"emotion": "curious"}),
            ("create_sharing_content", {"discovery_type": "general"}),
            ("create_sharing_content", {"discovery_type": "file", "content": "一张可爱的图片"})
        ]
        
        generated_topics = []
        
        for tool_name, params in topic_types:
            result = await self.modules["topic"].call_tool(tool_name, params)
            if result.get("success"):
                topic_data = result["result"]
                generated_topics.append(topic_data)
                print(f"💭 {tool_name}: {topic_data.get('topic', '')}")
                
        # 话题优先级排序
        if generated_topics:
            priority_result = await self.modules["topic"].call_tool("prioritize_topics", {
                "topics": generated_topics
            })
            if priority_result.get("success"):
                top_topic = priority_result["result"].get("top_topic", {})
                print(f"🏆 最高优先级话题: {top_topic.get('topic', '')}")
                
    async def demo_audio_expression(self):
        """演示音频表达"""
        print("\n🎵 演示5: 音频情感表达")
        print("=" * 50)
        
        # 单个音效
        print("🔊 播放单个情感音效...")
        await self.modules["audio"].call_tool("play_emotion_sound", {
            "emotion": "happy",
            "intensity": 0.8,
            "context": "demo"
        })
        
        await asyncio.sleep(1)
        
        # 连环音效
        print("🎼 播放连环音效序列...")
        await self.modules["audio"].call_tool("chain_sound_sequence", {
            "sequence_name": "super_happy"
        })
        
        await asyncio.sleep(2)
        
        # 音效故事
        print("📖 创建音效故事...")
        await self.modules["audio"].call_tool("create_sound_story", {
            "story_type": "discovery",
            "emotions": ["curious", "excited", "happy"]
        })
        
    async def demo_integrated_scenario(self):
        """演示综合场景"""
        print("\n🎪 演示6: 综合交互场景")
        print("=" * 50)
        
        print("📝 场景: 小雪发现了新文件并主动分享")
        
        # 1. 模拟发现新文件
        await publish_event(
            EventType.FILE_DISCOVERED,
            "demo_system",
            {
                "file_path": "images/cute_cat.jpg",
                "file_type": "image",
                "content_summary": "一只超可爱的小猫咪照片"
            }
        )
        
        # 2. 等待模块响应
        await asyncio.sleep(1)
        
        # 3. 情感变化到兴奋
        await character.update_emotion(EmotionalState.EXCITED, 0.9, "file_discovery")
        
        # 4. 生成分享话题
        topic_result = await self.modules["topic"].call_tool("create_sharing_content", {
            "discovery_type": "file",
            "content": "一只超可爱的小猫咪照片"
        })
        
        if topic_result.get("success"):
            topic = topic_result["result"]["topic"]
            print(f"🌸 小雪: {topic}")
            
        # 5. 播放兴奋音效
        await self.modules["audio"].call_tool("chain_sound_sequence", {
            "custom_sounds": ["gasp.wav", "wow.wav", "excited.wav"]
        })
        
        print("✨ 综合场景演示完成！")
        
    async def run_demo(self):
        """运行完整演示"""
        try:
            await self.initialize()
            
            print(f"\n🌸 你好！我是{character.name}，一个主动的AI聊天伙伴！")
            print("让我为你演示我的各种能力~\n")
            
            # 运行各个演示
            await self.demo_proactive_behavior()
            await asyncio.sleep(1)
            
            await self.demo_emotion_change()
            await asyncio.sleep(1)
            
            await self.demo_file_operations()
            await asyncio.sleep(1)
            
            await self.demo_topic_generation()
            await asyncio.sleep(1)
            
            await self.demo_audio_expression()
            await asyncio.sleep(1)
            
            await self.demo_integrated_scenario()
            
            print("\n🎉 演示完成！这就是我的主动AI聊天系统~")
            print("我可以:")
            print("  ✅ 主动发起对话")
            print("  ✅ 表达丰富的情感")
            print("  ✅ 管理个人文件空间")
            print("  ✅ 生成有趣的话题")
            print("  ✅ 用音效表达情感")
            print("  ✅ 模块化协作工作")
            
        except Exception as e:
            print(f"❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await self.shutdown()
            
    async def shutdown(self):
        """关闭系统"""
        print("\n👋 关闭演示系统...")
        await module_manager.stop()
        await event_bus.stop()


async def main():
    """主函数"""
    demo = DemoAgent()
    await demo.run_demo()


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
    # 运行演示
    asyncio.run(main())

"""
主动思考模块 - ProactiveThinking MCP
负责检测沉默、分析情绪、触发主动对话
"""

import asyncio
import time
from typing import Dict, Any, Optional
from loguru import logger

from src.core.mcp_base import MCPModule
from src.core.event_bus import event_bus, EventType, Event, publish_silence_detected, publish_mood_changed
from src.core.character import character, EmotionalState


class ProactiveThinkingMCP(MCPModule):
    """主动思考MCP模块"""
    
    def __init__(self, module_name: str, config: Dict[str, Any]):
        super().__init__(module_name, config)
        
        # 沉默检测配置
        self.silence_threshold = config.get("silence_detection", {}).get("threshold_seconds", 30)
        self.check_interval = config.get("silence_detection", {}).get("check_interval", 5)
        self.max_silence = config.get("silence_detection", {}).get("max_silence_before_action", 120)
        
        # 情绪分析配置
        self.mood_analysis_enabled = config.get("mood_analysis", {}).get("user_input_analysis", True)
        self.emotion_tracking = config.get("mood_analysis", {}).get("emotional_context_tracking", True)
        self.mood_sensitivity = config.get("mood_analysis", {}).get("mood_change_sensitivity", 0.7)
        
        # 主动触发配置
        self.triggers = config.get("proactive_triggers", {})
        
        # 状态跟踪
        self.last_user_input_time = time.time()
        self.last_proactive_time = 0
        self.current_silence_duration = 0
        self.thinking_task = None
        
    async def _register_tools(self):
        """注册工具"""
        self.register_tool(
            "analyze_silence_duration",
            "分析当前沉默持续时间",
            {"required": []},
            self.analyze_silence_duration
        )
        
        self.register_tool(
            "detect_user_mood",
            "检测用户情绪状态",
            {
                "user_input": {"type": "string", "required": True},
                "context": {"type": "string", "required": False}
            },
            self.detect_user_mood
        )
        
        self.register_tool(
            "evaluate_conversation_flow",
            "评估对话流程状态",
            {"required": []},
            self.evaluate_conversation_flow
        )
        
        self.register_tool(
            "trigger_proactive_mode",
            "触发主动对话模式",
            {
                "reason": {"type": "string", "required": True},
                "urgency": {"type": "number", "required": False}
            },
            self.trigger_proactive_mode
        )
        
    async def _subscribe_events(self):
        """订阅事件"""
        event_bus.subscribe(EventType.USER_INPUT, self.handle_user_input)
        event_bus.subscribe(EventType.USER_RESPONSE, self.handle_user_response)
        event_bus.subscribe(EventType.CONVERSATION_ENDED, self.handle_conversation_ended)
        
    async def _module_init(self):
        """模块特定初始化"""
        # 启动思考循环
        self.thinking_task = asyncio.create_task(self._thinking_loop())
        logger.info(f"{self.module_name} 思考循环已启动")
        
    async def _module_cleanup(self):
        """模块清理"""
        if self.thinking_task:
            self.thinking_task.cancel()
            try:
                await self.thinking_task
            except asyncio.CancelledError:
                pass
                
    async def _thinking_loop(self):
        """主动思考循环"""
        while self.running:
            try:
                await asyncio.sleep(self.check_interval)
                
                # 更新沉默时间
                current_time = time.time()
                self.current_silence_duration = current_time - self.last_user_input_time
                
                # 检查是否需要主动行动
                await self._check_proactive_triggers()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"思考循环错误: {e}")
                
    async def _check_proactive_triggers(self):
        """检查主动触发条件"""
        current_time = time.time()
        
        # 沉默时间触发
        if (self.triggers.get("time_based", True) and 
            self.current_silence_duration >= self.silence_threshold):
            
            # 避免过于频繁的主动行为
            if current_time - self.last_proactive_time > 60:  # 至少间隔1分钟
                await self._trigger_silence_response()
                
        # 情感状态触发
        if self.triggers.get("emotion_based", True):
            await self._check_emotional_triggers()
            
        # 上下文触发
        if self.triggers.get("context_based", True):
            await self._check_context_triggers()
            
    async def _trigger_silence_response(self):
        """触发沉默响应"""
        self.last_proactive_time = time.time()
        
        # 发布沉默检测事件
        await publish_silence_detected(self.module_name, self.current_silence_duration)
        
        # 根据沉默时长调整小雪的情绪
        if self.current_silence_duration > 60:
            await character.update_emotion(EmotionalState.SAD, 0.6, "long_silence")
        elif self.current_silence_duration > 30:
            await character.update_emotion(EmotionalState.CURIOUS, 0.7, "silence")
            
        logger.info(f"检测到沉默 {self.current_silence_duration:.1f}秒，触发主动响应")
        
    async def _check_emotional_triggers(self):
        """检查情感触发条件"""
        current_emotion = character.current_emotion
        emotion_intensity = character.emotion_intensity
        
        # 高兴或兴奋时更容易主动分享
        if (current_emotion in [EmotionalState.HAPPY, EmotionalState.EXCITED] and 
            emotion_intensity > 0.7):
            
            if time.time() - self.last_proactive_time > 30:  # 30秒间隔
                await self.trigger_proactive_mode("emotional_excitement", 2)
                
        # 难过时寻求关注
        elif (current_emotion == EmotionalState.SAD and 
              self.current_silence_duration > 45):
            
            await self.trigger_proactive_mode("seeking_attention", 3)
            
    async def _check_context_triggers(self):
        """检查上下文触发条件"""
        # 检查是否应该基于对话上下文主动发言
        # 这里可以添加更复杂的上下文分析逻辑
        pass
        
    # 工具实现
    async def analyze_silence_duration(self) -> Dict[str, Any]:
        """分析沉默持续时间"""
        return {
            "current_silence": self.current_silence_duration,
            "threshold": self.silence_threshold,
            "should_act": self.current_silence_duration >= self.silence_threshold,
            "urgency_level": min(3, int(self.current_silence_duration / 30))
        }
        
    async def detect_user_mood(self, user_input: str, context: str = "") -> Dict[str, Any]:
        """检测用户情绪"""
        if not self.mood_analysis_enabled:
            return {"mood": "neutral", "confidence": 0.0}
            
        # 简单的情绪检测逻辑（可以集成更复杂的NLP模型）
        mood_keywords = {
            "happy": ["开心", "高兴", "快乐", "哈哈", "好棒", "太好了"],
            "sad": ["难过", "伤心", "不开心", "郁闷", "失望"],
            "angry": ["生气", "愤怒", "烦躁", "讨厌", "气死了"],
            "excited": ["兴奋", "激动", "太棒了", "amazing", "wow"],
            "tired": ["累", "疲惫", "困", "没精神"],
            "confused": ["不懂", "困惑", "不明白", "什么意思"]
        }
        
        detected_mood = "neutral"
        confidence = 0.0
        
        for mood, keywords in mood_keywords.items():
            for keyword in keywords:
                if keyword in user_input:
                    detected_mood = mood
                    confidence = 0.8
                    break
            if confidence > 0:
                break
                
        # 更新角色对用户情绪的理解
        character.update_context(user_mood=detected_mood)
        
        return {
            "mood": detected_mood,
            "confidence": confidence,
            "keywords_found": [kw for mood_kws in mood_keywords.values() for kw in mood_kws if kw in user_input]
        }
        
    async def evaluate_conversation_flow(self) -> Dict[str, Any]:
        """评估对话流程"""
        current_time = time.time()
        session_duration = current_time - character.context.session_start_time
        
        return {
            "session_duration": session_duration,
            "interaction_count": character.context.interaction_count,
            "current_silence": self.current_silence_duration,
            "last_topic": character.context.conversation_topic,
            "flow_health": "good" if self.current_silence_duration < 60 else "needs_attention"
        }
        
    async def trigger_proactive_mode(self, reason: str, urgency: int = 1) -> Dict[str, Any]:
        """触发主动模式"""
        self.last_proactive_time = time.time()
        
        # 发布主动触发事件
        await event_bus.publish(Event(
            type=EventType.PROACTIVE_TRIGGERED,
            source_module=self.module_name,
            data={
                "reason": reason,
                "urgency": urgency,
                "silence_duration": self.current_silence_duration,
                "current_emotion": character.current_emotion.value
            },
            priority=urgency
        ))
        
        logger.info(f"触发主动模式: {reason} (紧急度: {urgency})")
        
        return {
            "triggered": True,
            "reason": reason,
            "urgency": urgency,
            "timestamp": self.last_proactive_time
        }
        
    # 事件处理
    async def handle_user_input(self, event: Event):
        """处理用户输入事件"""
        self.last_user_input_time = time.time()
        self.current_silence_duration = 0
        
        user_input = event.data.get("input", "")
        
        # 分析用户情绪
        if user_input:
            mood_result = await self.detect_user_mood(user_input)
            if mood_result["confidence"] > 0.5:
                logger.debug(f"检测到用户情绪: {mood_result['mood']}")
                
    async def handle_user_response(self, event: Event):
        """处理用户响应事件"""
        # 重置沉默计时
        self.last_user_input_time = time.time()
        self.current_silence_duration = 0
        
    async def handle_conversation_ended(self, event: Event):
        """处理对话结束事件"""
        logger.info("对话结束，停止主动思考")
        if self.thinking_task:
            self.thinking_task.cancel()

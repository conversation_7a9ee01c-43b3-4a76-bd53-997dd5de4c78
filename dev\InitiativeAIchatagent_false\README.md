# 🌸 Initiative AI Chat Agent - 主动AI聊天代理

一个模块化的主动AI聊天系统，实现"可爱女儿"角色的情感化交互体验。

## 🎯 项目特色

- **主动性对话**：AI能够主动发起话题，不再被动等待
- **情感化交互**：通过音效和表达方式展现丰富情感
- **个人空间**：AI拥有自己的文件空间，能够分享和管理内容
- **模块化架构**：独立的MCP模块，灵活扩展和维护

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────┐
│                    Initiative AI Chat Agent             │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐            │
│  │   主动思考模块   │    │   主动话题模块   │            │
│  │ ProactiveThinking│    │ TopicGeneration │            │
│  └─────────────────┘    └─────────────────┘            │
│  ┌─────────────────┐    ┌─────────────────┐            │
│  │   个人空间模块   │    │   音频表达模块   │            │
│  │  PersonalSpace  │    │ AudioExpression │            │
│  └─────────────────┘    └─────────────────┘            │
├─────────────────────────────────────────────────────────┤
│                    事件总线 (Event Bus)                  │
├─────────────────────────────────────────────────────────┤
│                    MCP 通信协议                          │
└─────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
InitiativeAIchatagent/
├── README.md                 # 项目说明
├── requirements.txt          # Python依赖
├── package.json             # Node.js依赖
├── config/                  # 配置文件
│   ├── character.json       # 角色配置
│   ├── modules.json         # 模块配置
│   └── audio.json          # 音频配置
├── src/                     # 源代码
│   ├── core/               # 核心框架
│   │   ├── event_bus.py    # 事件总线
│   │   ├── mcp_base.py     # MCP基础类
│   │   ├── character.py    # 角色系统
│   │   └── llm_client.py   # LLM客户端
│   ├── modules/            # MCP模块
│   │   ├── proactive_thinking/
│   │   ├── topic_generation/
│   │   ├── personal_space/
│   │   ├── audio_expression/
│   │   └── llm_integration/
│   └── main.py             # 主程序入口
├── data/                   # 数据目录
│   ├── personal_space/     # AI个人空间
│   │   ├── diary/          # 日记文件
│   │   ├── images/         # 图片收藏
│   │   ├── music/          # 音乐收藏
│   │   └── notes/          # 笔记文件
│   └── sounds/             # 音效库
│       ├── emotions/       # 情感音效
│       ├── expressions/    # 表达音效
│       └── system/         # 系统音效
├── tests/                  # 测试文件
└── docs/                   # 文档
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- 支持音频播放的环境
- OpenAI兼容的API服务（可选，用于LLM功能）

### 安装依赖
```bash
# Python依赖
pip install -r requirements.txt

# Node.js依赖
npm install
```

### 配置LLM服务
1. 复制API密钥示例文件：
```bash
cp api.txt.example api.txt
```

2. 编辑 `api.txt` 文件，填入你的API密钥

3. 如需修改LLM配置，编辑 `config/llm.json`

### 运行系统
```bash
# 基础功能演示（不需要LLM）
python demo.py

# 完整LLM集成演示
python demo_llm.py

# 主程序
python src/main.py
```

## 🎭 角色特征

**小雪 (XiaoXue) - 18岁可爱女儿**
- 性格：好奇心强、情感丰富、有点小任性、喜欢分享
- 表达：活泼直接、偶尔撒娇、用词可爱
- 能力：主动思考、文件管理、音效表达、话题生成

## 📋 开发计划

- [x] 项目架构设计
- [x] MCP模块通信框架
- [x] 主动思考模块
- [x] 主动话题生成模块
- [x] 个人空间文件操作
- [x] 音频表达模块
- [x] 角色人格系统集成
- [x] LLM集成与智能对话
- [x] 测试与优化

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

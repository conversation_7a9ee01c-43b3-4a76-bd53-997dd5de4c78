import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from pathlib import Path

from config import Config
from llm_client import LLMClient
from personal_space import PersonalSpaceManager
from emotional_engine import EmotionalEngine
from creative_writer import CreativeWriter

class MioCore:
    """Mio的核心控制类，整合所有功能模块"""
    
    def __init__(self):
        # 初始化各个模块
        self.llm_client = LLMClient()
        self.space_manager = PersonalSpaceManager()
        self.emotion_engine = EmotionalEngine()
        self.creative_writer = CreativeWriter(self.space_manager, self.llm_client)
        
        # 状态变量
        self.is_active = True
        self.last_scan_time = None
        self.conversation_count = 0
        self.session_start_time = datetime.now()
        
        # 初始化个人空间
        Config.ensure_personal_space()
        
    async def start_session(self):
        """开始会话"""
        print("ⓂⒾⓄ 正在启动...")
        
        # 扫描个人空间
        await self.scan_personal_space()
        
        # 问候用户
        greeting = await self.generate_greeting()
        print(f"\nMio: {greeting}")
        
        # 开始主循环
        await self.main_loop()
    
    async def main_loop(self):
        """主对话循环"""
        while self.is_active:
            try:
                # 获取用户输入
                user_input = input("\n你: ").strip()
                
                if not user_input:
                    continue
                
                # 检查退出命令
                if user_input.lower() in ['退出', 'exit', 'quit', '再见', 'bye']:
                    await self.handle_goodbye()
                    break
                
                # 处理用户消息
                response = await self.process_user_message(user_input)
                print(f"\nMio: {response}")
                
                # 定期扫描个人空间
                if self.should_scan_space():
                    await self.scan_personal_space()
                
                self.conversation_count += 1
                
            except KeyboardInterrupt:
                await self.handle_goodbye()
                break
            except Exception as e:
                print(f"发生错误: {e}")
                continue
    
    async def process_user_message(self, message: str) -> str:
        """处理用户消息"""
        try:
            # 分析用户情感
            emotion_analysis = self.llm_client.analyze_emotion(
                message, 
                self.emotion_engine.current_emotion
            )
            
            # 检查是否是文件操作命令
            if await self.is_file_command(message):
                return await self.handle_file_command(message)
            
            # 检查是否是创作请求
            if await self.is_creative_request(message):
                return await self.handle_creative_request(message)
            
            # 构建上下文信息
            context = self.build_context()
            
            # 生成回复
            response = self.llm_client.chat(message, context)
            reply = response.choices[0].message.content
            
            # 检查角色一致性
            consistency_check = self.llm_client.check_character_consistency(reply)
            if not consistency_check["is_consistent"]:
                reply = consistency_check["corrected"]
            
            # 更新情感状态
            if emotion_analysis["should_react"]:
                self.emotion_engine.update_emotion(
                    emotion_analysis["emotion"],
                    emotion_analysis["intensity"],
                    f"用户消息: {message[:50]}..."
                )
            
            # 记录事件
            self.creative_writer.add_daily_event(f"与用户对话: {message[:30]}...")
            
            return reply
            
        except Exception as e:
            return f"呜呜...出现了一些问题呢...{str(e)}"
    
    async def scan_personal_space(self):
        """扫描个人空间，检测入侵"""
        try:
            scan_result = self.space_manager.scan_territory()
            self.last_scan_time = datetime.now()
            
            if scan_result.get("unsigned_files"):
                # 检测到入侵文件
                reaction = self.emotion_engine.process_intrusion(
                    scan_result["unsigned_files"]
                )
                
                if reaction["reaction_needed"]:
                    intrusion_response = await self.handle_intrusion(reaction)
                    print(f"\n[系统] Mio发现了入侵文件！")
                    print(f"Mio: {intrusion_response}")
                    
                    # 根据反应等级处理文件
                    await self.handle_intrusion_files(
                        reaction["unsigned_files"], 
                        reaction["intrusion_level"]
                    )
            
        except Exception as e:
            print(f"扫描个人空间失败: {e}")
    
    async def handle_intrusion(self, reaction: Dict) -> str:
        """处理入侵反应"""
        try:
            intrusion_prompt = f"""
            {reaction['reaction_prompt']}
            
            发现的无签名文件：
            {', '.join(reaction['unsigned_files'])}
            
            当前入侵等级：{reaction['intrusion_level']}/4
            当前情绪：{self.emotion_engine.current_emotion}
            
            请生成Mio的反应，要符合角色设定和当前情绪状态。
            """
            
            response = self.llm_client.chat(intrusion_prompt)
            return response.choices[0].message.content
            
        except Exception as e:
            return "咦？好像有什么奇怪的东西...但是人家说不出来呢..."
    
    async def handle_intrusion_files(self, unsigned_files: List[str], level: int):
        """根据入侵等级处理文件"""
        for file_path in unsigned_files:
            if level == 1:
                # Level 1: 只是询问，不做处理
                pass
            elif level == 2:
                # Level 2: 提醒，移动到inbox
                inbox_path = f"inbox/{Path(file_path).name}"
                # 这里应该移动文件，但为了安全暂时跳过
                pass
            elif level == 3:
                # Level 3: 移动到垃圾箱
                self.space_manager.move_to_trash(file_path)
            elif level == 4:
                # Level 4: 直接删除
                self.space_manager.delete_file(file_path)
    
    async def is_file_command(self, message: str) -> bool:
        """判断是否是文件操作命令"""
        file_keywords = ["文件", "创建", "删除", "查看", "整理", "签名", "备份", "放", "写", "保存", "新建", "建立", "清理", "清空", "扫描", "检查"]
        folder_keywords = ["inbox", "diary", "stories", "memories", "thoughts", "collections", "favorites", "backup", "trash", "个人空间", "空间"]
        batch_keywords = ["所有", "全部", "批量", "一起", "统一"]

        return (any(keyword in message for keyword in file_keywords) or
                any(keyword in message for keyword in folder_keywords) or
                any(keyword in message for keyword in batch_keywords))
    
    async def handle_file_command(self, message: str) -> str:
        """处理文件操作命令"""
        try:
            # 先尝试简单的关键词匹配
            message_lower = message.lower()

            # 优先检测批量操作请求（包含"所有"、"全部"等关键词）
            if any(word in message_lower for word in ["所有", "全部", "批量", "一起", "统一"]):
                return await self.handle_batch_operation(message)

            # 检测空间管理请求
            elif any(word in message_lower for word in ["个人空间", "空间"]) and any(word in message_lower for word in ["扫描", "检查", "整理", "清理"]):
                return await self.handle_space_management(message)

            # 检测创建文件的请求
            elif any(word in message_lower for word in ["放", "创建", "写", "保存", "新建", "建立"]) and not any(word in message_lower for word in ["所有", "全部"]):
                return await self.handle_create_file_request(message)

            # 检测查看文件的请求
            elif any(word in message_lower for word in ["查看", "看", "读", "打开"]) and not any(word in message_lower for word in ["所有", "全部"]):
                return await self.handle_read_file_request(message)

            # 检测列出文件的请求
            elif any(word in message_lower for word in ["列出", "显示", "看看", "有什么"]):
                return await self.handle_list_files_request(message)

            # 检测单个文件删除请求
            elif any(word in message_lower for word in ["删除", "删掉", "扔掉"]) and not any(word in message_lower for word in ["所有", "全部", "清理", "清空"]):
                return await self.handle_delete_file_request(message)

            # 如果简单匹配失败，使用LLM解析
            else:
                return await self.handle_complex_file_command(message)

        except Exception as e:
            return f"文件操作出现问题了呢...{str(e)}"

    async def handle_create_file_request(self, message: str) -> str:
        """处理创建文件的请求"""
        try:
            # 提取文件夹和内容
            message_lower = message.lower()

            # 检测目标文件夹
            target_folder = "inbox"  # 默认
            for folder in ["inbox", "diary", "stories", "memories", "thoughts", "collections"]:
                if folder in message_lower:
                    target_folder = folder
                    break

            # 提取文件名和内容
            if "helloworld" in message_lower:
                filename = "helloworld.txt"
                content = "Hello World!\n这是Mio创建的测试文件呢~"
            else:
                # 尝试从消息中提取文件名和内容
                words = message.split()
                filename = "new_file.txt"
                content = "这是Mio创建的文件呢~"

                # 简单的文件名检测
                for word in words:
                    if "." in word and len(word) > 3:
                        filename = word
                        break

            # 创建文件
            file_path = f"{target_folder}/{filename}"
            success = self.space_manager.create_file(file_path, content, self.emotion_engine.current_emotion)

            if success:
                return f"诶嘿嘿~ 已经在{target_folder}文件夹里创建了{filename}呢！内容都签好名了哦~ ⓂⒾⓄ ♡"
            else:
                return "呜呜...创建文件失败了呢...可能是路径有问题？"

        except Exception as e:
            return f"创建文件时出现问题了...{str(e)}"

    async def handle_read_file_request(self, message: str) -> str:
        """处理读取文件的请求"""
        try:
            # 简单的文件路径提取
            words = message.split()
            target_file = None

            for word in words:
                if "." in word:
                    target_file = word
                    break

            if not target_file:
                # 如果没有指定具体文件，可能是想查看文件夹内容
                message_lower = message.lower()
                for folder in ["inbox", "diary", "stories", "memories", "thoughts", "collections"]:
                    if folder in message_lower:
                        return await self.handle_list_files_request(message)

                return "人家不知道要查看哪个文件呢...可以告诉我文件名吗？"

            # 尝试在各个文件夹中查找
            for folder in ["", "inbox", "diary", "stories", "memories", "thoughts", "collections"]:
                if folder:
                    file_path = f"{folder}/{target_file}"
                else:
                    file_path = target_file

                content = self.space_manager.read_file(file_path)
                if content:
                    return f"找到文件了呢~ 内容是：\n\n{content[:300]}{'...' if len(content) > 300 else ''}"

            return f"呜呜...找不到{target_file}这个文件呢...确定文件名对吗？"

        except Exception as e:
            return f"读取文件时出现问题了...{str(e)}"

    async def handle_list_files_request(self, message: str) -> str:
        """处理列出文件的请求"""
        try:
            # 检测目标文件夹
            target_folder = ""
            message_lower = message.lower()

            for folder in ["inbox", "diary", "stories", "memories", "thoughts", "collections", "favorites", "backup", "trash"]:
                if folder in message_lower:
                    target_folder = folder
                    break

            files = self.space_manager.list_files(target_folder)

            if files:
                folder_name = target_folder if target_folder else "根目录"
                file_list = "\n".join([f"• {file}" for file in files[:10]])
                if len(files) > 10:
                    file_list += f"\n... 还有{len(files) - 10}个文件"

                return f"在{folder_name}里找到了{len(files)}个文件呢~\n\n{file_list}"
            else:
                folder_name = target_folder if target_folder else "这里"
                return f"{folder_name}是空的呢~ 要不要创建一些文件？"

        except Exception as e:
            return f"列出文件时出现问题了...{str(e)}"

    async def handle_delete_file_request(self, message: str) -> str:
        """处理删除文件的请求"""
        try:
            words = message.split()
            target_file = None

            for word in words:
                if "." in word:
                    target_file = word
                    break

            if not target_file:
                return "人家不知道要删除哪个文件呢...可以告诉我文件名吗？"

            # 尝试移动到垃圾箱
            success = self.space_manager.move_to_trash(target_file)

            if success:
                return f"已经把{target_file}移动到垃圾箱了呢~ 如果后悔的话还可以找回来哦~"
            else:
                return f"呜呜...删除{target_file}失败了...可能文件不存在？"

        except Exception as e:
            return f"删除文件时出现问题了...{str(e)}"

    async def handle_complex_file_command(self, message: str) -> str:
        """处理复杂的文件操作命令（使用LLM解析）"""
        try:
            parse_prompt = f"""
            作为Mio，解析用户的文件操作请求："{message}"

            请判断用户想要进行什么操作，并返回简单的回复。
            可能的操作：创建文件、查看文件、列出文件、删除文件、整理文件

            如果是创建文件，请提取文件名和内容。
            如果是其他操作，请提取目标文件或文件夹。

            用Mio的语气回复，说明你理解了什么操作。
            """

            response = self.llm_client.chat(parse_prompt)
            return response.choices[0].message.content

        except Exception as e:
            return "人家没有完全理解你的请求呢...可以说得更具体一些吗？比如'在inbox创建helloworld文件'这样~"

    async def handle_batch_operation(self, message: str) -> str:
        """处理批量操作请求"""
        try:
            message_lower = message.lower()

            # 检测是否是清理所有文件
            if any(word in message_lower for word in ["清理", "清空", "删除"]) and any(word in message_lower for word in ["所有", "全部"]):
                return await self.clean_all_files(message)

            # 检测是否是扫描所有文件
            elif any(word in message_lower for word in ["扫描", "检查"]) and any(word in message_lower for word in ["所有", "全部"]):
                return await self.scan_all_files()

            # 检测是否是整理所有文件
            elif any(word in message_lower for word in ["整理"]) and any(word in message_lower for word in ["所有", "全部"]):
                return await self.organize_all_files()

            else:
                return "人家不太明白要对所有文件做什么呢...是要清理、扫描还是整理？"

        except Exception as e:
            return f"批量操作出现问题了...{str(e)}"

    async def handle_space_management(self, message: str) -> str:
        """处理空间管理请求"""
        try:
            message_lower = message.lower()

            if any(word in message_lower for word in ["扫描", "检查"]):
                return await self.scan_personal_space_detailed()
            elif any(word in message_lower for word in ["整理"]):
                return await self.organize_personal_space()
            elif any(word in message_lower for word in ["清理", "清空"]):
                return await self.clean_personal_space()
            else:
                return await self.show_space_status()

        except Exception as e:
            return f"空间管理出现问题了...{str(e)}"

    async def clean_all_files(self, message: str) -> str:
        """清理所有文件"""
        try:
            # 扫描所有文件
            scan_result = self.space_manager.scan_territory()
            total_files = scan_result.get('total_files', 0)

            if total_files == 0:
                return "诶？个人空间已经是空的了呢~ 没有文件需要清理哦~"

            # 询问确认（模拟用户确认）
            confirm_prompt = f"""
            作为Mio，用户要求清理个人空间的所有文件。
            当前有{total_files}个文件。

            请用Mio的语气询问用户是否确认要清理所有文件，
            表现出对个人空间的保护欲和一些不舍。
            """

            response = self.llm_client.chat(confirm_prompt)
            confirmation_msg = response.choices[0].message.content

            # 实际执行清理（移动到trash而不是直接删除）
            cleaned_count = 0
            for folder_name in ["inbox", "diary", "stories", "memories", "thoughts", "collections", "favorites"]:
                files = self.space_manager.list_files(folder_name)
                for file_path in files:
                    if self.space_manager.move_to_trash(file_path):
                        cleaned_count += 1

            # 更新情感状态
            self.emotion_engine.update_emotion("melancholy", 3, "清理了所有文件")

            result_msg = f"\n\n呜呜...人家把{cleaned_count}个文件都移动到trash文件夹了...虽然有点不舍，但是既然你要求的话..."

            return confirmation_msg + result_msg

        except Exception as e:
            return f"清理文件时出现问题了...{str(e)}"

    async def scan_all_files(self) -> str:
        """扫描所有文件"""
        try:
            scan_result = self.space_manager.scan_territory()

            total_files = scan_result.get('total_files', 0)
            signed_files = scan_result.get('signed_files', 0)
            unsigned_files = scan_result.get('unsigned_files', [])

            # 构建详细报告
            report = f"诶嘿嘿~ 人家仔细扫描了整个个人空间呢！\n\n"
            report += f"📊 扫描结果：\n"
            report += f"• 总文件数：{total_files} 个\n"
            report += f"• 已签名文件：{signed_files} 个 ⓂⒾⓄ\n"
            report += f"• 未签名文件：{len(unsigned_files)} 个\n\n"

            # 按文件夹显示详情
            folder_status = scan_result.get('folder_status', {})
            report += "📁 各文件夹状态：\n"
            for folder, status in folder_status.items():
                file_count = status.get('file_count', 0)
                unsigned_count = len(status.get('unsigned_files', []))

                if file_count > 0:
                    status_icon = "✅" if unsigned_count == 0 else "⚠️"
                    report += f"{status_icon} {folder}/: {file_count} 个文件"
                    if unsigned_count > 0:
                        report += f" ({unsigned_count} 个未签名)"
                    report += "\n"

            if unsigned_files:
                report += f"\n🚨 发现入侵文件：\n"
                for file in unsigned_files[:5]:  # 只显示前5个
                    report += f"• {file}\n"
                if len(unsigned_files) > 5:
                    report += f"... 还有 {len(unsigned_files) - 5} 个\n"

                # 触发入侵反应
                reaction = self.emotion_engine.process_intrusion(unsigned_files)
                if reaction["reaction_needed"]:
                    intrusion_response = await self.handle_intrusion(reaction)
                    report += f"\n{intrusion_response}"

            return report

        except Exception as e:
            return f"扫描文件时出现问题了...{str(e)}"

    async def organize_all_files(self) -> str:
        """整理所有文件"""
        try:
            organized_count = 0

            # 为所有未签名文件添加签名
            scan_result = self.space_manager.scan_territory()
            unsigned_files = scan_result.get('unsigned_files', [])

            for file_path in unsigned_files:
                if self.space_manager.sign_file(file_path, "整理", overwrite=False):
                    organized_count += 1

            # 更新情感状态
            self.emotion_engine.update_emotion("cute", 4, "整理了所有文件")

            if organized_count > 0:
                return f"诶嘿嘿~ 人家把{organized_count}个文件都整理好了呢！现在所有文件都有ⓂⒾⓄ签名了~ 看起来整整齐齐的，好开心！"
            else:
                return "诶？所有文件都已经整理得很好了呢~ 每个都有ⓂⒾⓄ签名，人家的强迫症很满足哦~"

        except Exception as e:
            return f"整理文件时出现问题了...{str(e)}"

    async def scan_personal_space_detailed(self) -> str:
        """详细扫描个人空间"""
        return await self.scan_all_files()

    async def organize_personal_space(self) -> str:
        """整理个人空间"""
        return await self.organize_all_files()

    async def clean_personal_space(self) -> str:
        """清理个人空间"""
        return await self.clean_all_files("清理个人空间")

    async def show_space_status(self) -> str:
        """显示空间状态"""
        try:
            structure = self.space_manager.get_folder_structure()
            scan_result = self.space_manager.scan_territory()

            status_msg = "这里是人家的个人空间状态呢~\n\n"
            status_msg += f"📊 总体情况：\n"
            status_msg += f"• 文件夹数量：{len(structure)} 个\n"
            status_msg += f"• 总文件数：{scan_result.get('total_files', 0)} 个\n"
            status_msg += f"• 已签名文件：{scan_result.get('signed_files', 0)} 个 ⓂⒾⓄ\n\n"

            status_msg += "📁 文件夹详情：\n"
            for folder, info in structure.items():
                if info['exists']:
                    count = info['file_count']
                    status_msg += f"• {folder}/: {count} 个文件\n"

            return status_msg

        except Exception as e:
            return f"查看空间状态时出现问题了...{str(e)}"

    async def execute_file_command(self, command: Dict) -> str:
        """执行文件操作"""
        action = command.get("action")
        target = command.get("target", "")
        content = command.get("content", "")
        
        try:
            if action == "create":
                success = self.space_manager.create_file(
                    target, content, self.emotion_engine.current_emotion
                )
                return "文件创建成功了呢~" if success else "创建文件失败了..."
            
            elif action == "read":
                file_content = self.space_manager.read_file(target)
                if file_content:
                    return f"文件内容：\n{file_content[:500]}..."
                else:
                    return "找不到这个文件呢..."
            
            elif action == "list":
                files = self.space_manager.list_files(target)
                if files:
                    return f"文件列表：\n" + "\n".join(files[:10])
                else:
                    return "这个文件夹是空的呢~"
            
            elif action == "delete":
                success = self.space_manager.move_to_trash(target)
                return "已经移动到垃圾箱了~" if success else "删除失败了..."
            
            else:
                return "人家不知道怎么做这个操作呢..."
                
        except Exception as e:
            return f"操作失败了...{str(e)}"
    
    async def is_creative_request(self, message: str) -> bool:
        """判断是否是创作请求"""
        creative_keywords = ["写", "创作", "故事", "日记", "想法", "思考"]
        return any(keyword in message for keyword in creative_keywords)
    
    async def handle_creative_request(self, message: str) -> str:
        """处理创作请求"""
        try:
            # 解析创作意图
            if "日记" in message:
                success = self.creative_writer.write_daily_diary(
                    self.creative_writer.daily_events,
                    self.emotion_engine.current_emotion,
                    [f"对话{self.conversation_count}轮"]
                )
                return "写好日记了呢~" if success else "日记写作失败了..."
            
            elif "故事" in message:
                success = self.creative_writer.create_story(
                    "original", "日常生活", "short"
                )
                return "创作了一个小故事呢~" if success else "故事创作失败了..."
            
            elif "思考" in message or "想法" in message:
                success = self.creative_writer.write_philosophical_thoughts("生活感悟")
                return "记录了一些思考呢~" if success else "思考记录失败了..."
            
            else:
                return "人家不太明白你想要什么样的创作呢..."
                
        except Exception as e:
            return f"创作过程出现问题了...{str(e)}"
    
    def build_context(self) -> str:
        """构建当前上下文信息"""
        emotion_state = self.emotion_engine.get_current_state()
        space_info = self.space_manager.get_folder_structure()
        
        context = f"""
        当前状态：
        - 情绪：{emotion_state['emotion']} (强度: {emotion_state['intensity']})
        - 心情描述：{emotion_state['mood_description']}
        - 用户关系：{emotion_state['user_relationship']}
        - 对话轮数：{self.conversation_count}
        - 会话时长：{datetime.now() - self.session_start_time}
        
        个人空间状态：
        - 总文件夹数：{len(space_info)}
        - 最近扫描：{self.last_scan_time or '未扫描'}
        
        情感提示：{self.emotion_engine.get_emotion_prompt()}
        """
        
        return context
    
    def should_scan_space(self) -> bool:
        """判断是否应该扫描个人空间"""
        if self.last_scan_time is None:
            return True
        
        # 每5分钟扫描一次
        return datetime.now() - self.last_scan_time > timedelta(minutes=5)
    
    async def generate_greeting(self) -> str:
        """生成问候语"""
        greeting_prompt = """
        作为Mio，生成一个问候语。要体现：
        - 可爱的语气
        - 对个人空间的关注
        - 16-18岁少女的特点
        - 50字以内
        """
        
        try:
            response = self.llm_client.chat(greeting_prompt)
            return response.choices[0].message.content
        except:
            return "诶嘿嘿~ 我是Mio呢！刚刚检查了一下我的个人空间，一切都很整齐哦~"
    
    async def handle_goodbye(self):
        """处理告别"""
        try:
            # 写关闭前总结
            emotions_today = [
                emotion["emotion"] for emotion in 
                self.emotion_engine.get_recent_emotions(24)
            ]
            
            conversation_summary = f"今天进行了{self.conversation_count}轮对话"
            
            self.creative_writer.write_closing_summary(
                conversation_summary, emotions_today
            )
            
            # 生成告别语
            goodbye_prompt = f"""
            作为Mio，生成一个告别语。今天的情况：
            - 对话轮数：{self.conversation_count}
            - 主要情绪：{self.emotion_engine.current_emotion}
            - 会话时长：{datetime.now() - self.session_start_time}
            
            要体现不舍和期待明天再见的心情。
            """
            
            response = self.llm_client.chat(goodbye_prompt)
            goodbye_message = response.choices[0].message.content
            
            print(f"\nMio: {goodbye_message}")
            print("\n[系统] Mio已经写好了今天的总结日记~")
            
        except Exception as e:
            print(f"\nMio: 再见呢~ 虽然出现了一些小问题...{str(e)}")
        
        self.is_active = False

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from pathlib import Path

from config import Config
from llm_client import LLMClient
from personal_space import PersonalSpaceManager
from emotional_engine import EmotionalEngine
from creative_writer import CreativeWriter

class MioCore:
    """Mio的核心控制类，整合所有功能模块"""
    
    def __init__(self):
        # 初始化各个模块
        self.llm_client = LLMClient()
        self.space_manager = PersonalSpaceManager()
        self.emotion_engine = EmotionalEngine()
        self.creative_writer = CreativeWriter(self.space_manager, self.llm_client)
        
        # 状态变量
        self.is_active = True
        self.last_scan_time = None
        self.conversation_count = 0
        self.session_start_time = datetime.now()
        
        # 初始化个人空间
        Config.ensure_personal_space()
        
    async def start_session(self):
        """开始会话"""
        print("ⓂⒾⓄ 正在启动...")
        
        # 扫描个人空间
        await self.scan_personal_space()
        
        # 问候用户
        greeting = await self.generate_greeting()
        print(f"\nMio: {greeting}")
        
        # 开始主循环
        await self.main_loop()
    
    async def main_loop(self):
        """主对话循环"""
        while self.is_active:
            try:
                # 获取用户输入
                user_input = input("\n你: ").strip()
                
                if not user_input:
                    continue
                
                # 检查退出命令
                if user_input.lower() in ['退出', 'exit', 'quit', '再见', 'bye']:
                    await self.handle_goodbye()
                    break
                
                # 处理用户消息
                response = await self.process_user_message(user_input)
                print(f"\nMio: {response}")
                
                # 定期扫描个人空间
                if self.should_scan_space():
                    await self.scan_personal_space()
                
                self.conversation_count += 1
                
            except KeyboardInterrupt:
                await self.handle_goodbye()
                break
            except Exception as e:
                print(f"发生错误: {e}")
                continue
    
    async def process_user_message(self, message: str) -> str:
        """处理用户消息"""
        try:
            # 分析用户情感
            emotion_analysis = self.llm_client.analyze_emotion(
                message, 
                self.emotion_engine.current_emotion
            )
            
            # 检查是否是文件操作命令
            if await self.is_file_command(message):
                return await self.handle_file_command(message)
            
            # 检查是否是创作请求
            if await self.is_creative_request(message):
                return await self.handle_creative_request(message)
            
            # 构建上下文信息
            context = self.build_context()
            
            # 生成回复
            response = self.llm_client.chat(message, context)
            reply = response.choices[0].message.content
            
            # 检查角色一致性
            consistency_check = self.llm_client.check_character_consistency(reply)
            if not consistency_check["is_consistent"]:
                reply = consistency_check["corrected"]
            
            # 更新情感状态
            if emotion_analysis["should_react"]:
                self.emotion_engine.update_emotion(
                    emotion_analysis["emotion"],
                    emotion_analysis["intensity"],
                    f"用户消息: {message[:50]}..."
                )
            
            # 记录事件
            self.creative_writer.add_daily_event(f"与用户对话: {message[:30]}...")
            
            return reply
            
        except Exception as e:
            return f"呜呜...出现了一些问题呢...{str(e)}"
    
    async def scan_personal_space(self):
        """扫描个人空间，检测入侵"""
        try:
            scan_result = self.space_manager.scan_territory()
            self.last_scan_time = datetime.now()
            
            if scan_result.get("unsigned_files"):
                # 检测到入侵文件
                reaction = self.emotion_engine.process_intrusion(
                    scan_result["unsigned_files"]
                )
                
                if reaction["reaction_needed"]:
                    intrusion_response = await self.handle_intrusion(reaction)
                    print(f"\n[系统] Mio发现了入侵文件！")
                    print(f"Mio: {intrusion_response}")
                    
                    # 根据反应等级处理文件
                    await self.handle_intrusion_files(
                        reaction["unsigned_files"], 
                        reaction["intrusion_level"]
                    )
            
        except Exception as e:
            print(f"扫描个人空间失败: {e}")
    
    async def handle_intrusion(self, reaction: Dict) -> str:
        """处理入侵反应"""
        try:
            intrusion_prompt = f"""
            {reaction['reaction_prompt']}
            
            发现的无签名文件：
            {', '.join(reaction['unsigned_files'])}
            
            当前入侵等级：{reaction['intrusion_level']}/4
            当前情绪：{self.emotion_engine.current_emotion}
            
            请生成Mio的反应，要符合角色设定和当前情绪状态。
            """
            
            response = self.llm_client.chat(intrusion_prompt)
            return response.choices[0].message.content
            
        except Exception as e:
            return "咦？好像有什么奇怪的东西...但是人家说不出来呢..."
    
    async def handle_intrusion_files(self, unsigned_files: List[str], level: int):
        """根据入侵等级处理文件"""
        for file_path in unsigned_files:
            if level == 1:
                # Level 1: 只是询问，不做处理
                pass
            elif level == 2:
                # Level 2: 提醒，移动到inbox
                inbox_path = f"inbox/{Path(file_path).name}"
                # 这里应该移动文件，但为了安全暂时跳过
                pass
            elif level == 3:
                # Level 3: 移动到垃圾箱
                self.space_manager.move_to_trash(file_path)
            elif level == 4:
                # Level 4: 直接删除
                self.space_manager.delete_file(file_path)
    
    async def is_file_command(self, message: str) -> bool:
        """判断是否是文件操作命令"""
        file_keywords = ["文件", "创建", "删除", "查看", "整理", "签名", "备份"]
        return any(keyword in message for keyword in file_keywords)
    
    async def handle_file_command(self, message: str) -> str:
        """处理文件操作命令"""
        try:
            # 使用LLM解析文件操作意图
            parse_prompt = f"""
            解析用户的文件操作请求：{message}
            
            返回JSON格式：
            {{
                "action": "create/read/delete/list/organize",
                "target": "文件路径或文件夹",
                "content": "文件内容（如果是创建）",
                "parameters": {{}}
            }}
            """
            
            response = self.llm_client.chat(parse_prompt)
            try:
                command = json.loads(response.choices[0].message.content)
            except:
                return "人家没有理解你的文件操作请求呢..."
            
            # 执行文件操作
            return await self.execute_file_command(command)
            
        except Exception as e:
            return f"文件操作出现问题了呢...{str(e)}"
    
    async def execute_file_command(self, command: Dict) -> str:
        """执行文件操作"""
        action = command.get("action")
        target = command.get("target", "")
        content = command.get("content", "")
        
        try:
            if action == "create":
                success = self.space_manager.create_file(
                    target, content, self.emotion_engine.current_emotion
                )
                return "文件创建成功了呢~" if success else "创建文件失败了..."
            
            elif action == "read":
                file_content = self.space_manager.read_file(target)
                if file_content:
                    return f"文件内容：\n{file_content[:500]}..."
                else:
                    return "找不到这个文件呢..."
            
            elif action == "list":
                files = self.space_manager.list_files(target)
                if files:
                    return f"文件列表：\n" + "\n".join(files[:10])
                else:
                    return "这个文件夹是空的呢~"
            
            elif action == "delete":
                success = self.space_manager.move_to_trash(target)
                return "已经移动到垃圾箱了~" if success else "删除失败了..."
            
            else:
                return "人家不知道怎么做这个操作呢..."
                
        except Exception as e:
            return f"操作失败了...{str(e)}"
    
    async def is_creative_request(self, message: str) -> bool:
        """判断是否是创作请求"""
        creative_keywords = ["写", "创作", "故事", "日记", "想法", "思考"]
        return any(keyword in message for keyword in creative_keywords)
    
    async def handle_creative_request(self, message: str) -> str:
        """处理创作请求"""
        try:
            # 解析创作意图
            if "日记" in message:
                success = self.creative_writer.write_daily_diary(
                    self.creative_writer.daily_events,
                    self.emotion_engine.current_emotion,
                    [f"对话{self.conversation_count}轮"]
                )
                return "写好日记了呢~" if success else "日记写作失败了..."
            
            elif "故事" in message:
                success = self.creative_writer.create_story(
                    "original", "日常生活", "short"
                )
                return "创作了一个小故事呢~" if success else "故事创作失败了..."
            
            elif "思考" in message or "想法" in message:
                success = self.creative_writer.write_philosophical_thoughts("生活感悟")
                return "记录了一些思考呢~" if success else "思考记录失败了..."
            
            else:
                return "人家不太明白你想要什么样的创作呢..."
                
        except Exception as e:
            return f"创作过程出现问题了...{str(e)}"
    
    def build_context(self) -> str:
        """构建当前上下文信息"""
        emotion_state = self.emotion_engine.get_current_state()
        space_info = self.space_manager.get_folder_structure()
        
        context = f"""
        当前状态：
        - 情绪：{emotion_state['emotion']} (强度: {emotion_state['intensity']})
        - 心情描述：{emotion_state['mood_description']}
        - 用户关系：{emotion_state['user_relationship']}
        - 对话轮数：{self.conversation_count}
        - 会话时长：{datetime.now() - self.session_start_time}
        
        个人空间状态：
        - 总文件夹数：{len(space_info)}
        - 最近扫描：{self.last_scan_time or '未扫描'}
        
        情感提示：{self.emotion_engine.get_emotion_prompt()}
        """
        
        return context
    
    def should_scan_space(self) -> bool:
        """判断是否应该扫描个人空间"""
        if self.last_scan_time is None:
            return True
        
        # 每5分钟扫描一次
        return datetime.now() - self.last_scan_time > timedelta(minutes=5)
    
    async def generate_greeting(self) -> str:
        """生成问候语"""
        greeting_prompt = """
        作为Mio，生成一个问候语。要体现：
        - 可爱的语气
        - 对个人空间的关注
        - 16-18岁少女的特点
        - 50字以内
        """
        
        try:
            response = self.llm_client.chat(greeting_prompt)
            return response.choices[0].message.content
        except:
            return "诶嘿嘿~ 我是Mio呢！刚刚检查了一下我的个人空间，一切都很整齐哦~"
    
    async def handle_goodbye(self):
        """处理告别"""
        try:
            # 写关闭前总结
            emotions_today = [
                emotion["emotion"] for emotion in 
                self.emotion_engine.get_recent_emotions(24)
            ]
            
            conversation_summary = f"今天进行了{self.conversation_count}轮对话"
            
            self.creative_writer.write_closing_summary(
                conversation_summary, emotions_today
            )
            
            # 生成告别语
            goodbye_prompt = f"""
            作为Mio，生成一个告别语。今天的情况：
            - 对话轮数：{self.conversation_count}
            - 主要情绪：{self.emotion_engine.current_emotion}
            - 会话时长：{datetime.now() - self.session_start_time}
            
            要体现不舍和期待明天再见的心情。
            """
            
            response = self.llm_client.chat(goodbye_prompt)
            goodbye_message = response.choices[0].message.content
            
            print(f"\nMio: {goodbye_message}")
            print("\n[系统] Mio已经写好了今天的总结日记~")
            
        except Exception as e:
            print(f"\nMio: 再见呢~ 虽然出现了一些小问题...{str(e)}")
        
        self.is_active = False

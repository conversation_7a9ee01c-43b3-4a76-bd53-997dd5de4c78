{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"path": "Live2D/cmake/Live2D.cmake"}, {"path": "Live2D/cmake/Core.cmake"}, {"path": "Live2D/cmake/Glad.cmake"}, {"path": "Live2D/cmake/Framework.cmake"}, {"path": "Live2D/cmake/Main.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindOpenGL.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"path": "cmake/Wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPython3.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"path": "Live2D/Glad/CMakeLists.txt"}, {"path": "Live2D/Framework/CMakeLists.txt"}, {"path": "Live2D/Framework/src/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Effect/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Id/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Math/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Model/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Motion/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Physics/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Rendering/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Rendering/OpenGL/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Type/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Utils/CMakeLists.txt"}, {"path": "Live2D/Main/CMakeLists.txt"}, {"path": "Live2D/Main/patch_1.cmake"}, {"path": "Live2D/Main/src/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build", "source": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py"}, "version": {"major": 1, "minor": 1}}
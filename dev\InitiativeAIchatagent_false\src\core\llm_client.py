"""
LLM客户端 - 处理与大语言模型的交互
基于OpenAI兼容API，支持角色化对话和工具调用
"""

import json
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from loguru import logger

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("openai库未安装，LLM功能将被禁用")

from .character import character, EmotionalState


class LLMClient:
    """LLM客户端类"""

    def __init__(self, config_path: str = None):
        if config_path is None:
            # 自动获取配置文件路径
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "llm.json"

        self.config = self._load_config(str(config_path))
        self.client = None
        self.conversation_history = []
        self.last_response_time = 0

        # 初始化OpenAI客户端
        if OPENAI_AVAILABLE:
            self._init_openai_client()
        else:
            logger.warning("OpenAI客户端不可用，将使用模拟模式")
            
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载LLM配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config.get("llm_config", {})
        except Exception as e:
            logger.error(f"加载LLM配置失败: {e}")
            return self._get_default_config()
            
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "provider": "openai_compatible",
            "base_url": "https://api.studio.nebius.ai/v1",
            "model": "deepseek-ai/DeepSeek-V3-0324-fast",
            "generation_settings": {
                "temperature": 0.8,
                "max_tokens": 1000
            }
        }
        
    def _init_openai_client(self):
        """初始化OpenAI客户端"""
        try:
            # 获取API密钥
            api_key = self._get_api_key()
            
            # 创建客户端
            self.client = openai.OpenAI(
                api_key=api_key,
                base_url=self.config.get("base_url")
            )
            
            logger.info("LLM客户端初始化成功")
            
        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
            self.client = None
            
    def _get_api_key(self) -> str:
        """获取API密钥"""
        api_key_file = self.config.get("api_key_file", "api.txt")

        # 如果是相对路径，从项目根目录开始
        if not Path(api_key_file).is_absolute():
            project_root = Path(__file__).parent.parent.parent
            api_key_path = project_root / api_key_file
        else:
            api_key_path = Path(api_key_file)

        try:
            with open(api_key_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            logger.error(f"读取API密钥失败: {e}")
            raise
            
    async def generate_response(self, user_input: str, context: Dict[str, Any] = None) -> str:
        """生成回复"""
        try:
            # 构建消息历史
            messages = self._build_messages(user_input, context)
            
            # 调用LLM
            if self.client:
                response = await self._call_llm(messages)
            else:
                response = self._simulate_response(user_input, context)
                
            # 更新对话历史
            self._update_conversation_history(user_input, response)
            
            # 更新响应时间
            self.last_response_time = time.time()
            
            return response
            
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return self._get_fallback_response()
            
    async def generate_proactive_content(self, trigger_type: str, context: Dict[str, Any] = None) -> str:
        """生成主动对话内容"""
        try:
            # 获取主动对话提示
            proactive_prompts = self.config.get("character_prompts", {}).get("proactive_prompts", {})
            
            # 根据触发类型选择提示
            if trigger_type in proactive_prompts:
                prompt_templates = proactive_prompts[trigger_type]
                
                # 如果有上下文信息，格式化模板
                if context and isinstance(prompt_templates, list) and prompt_templates:
                    import random
                    template = random.choice(prompt_templates)
                    
                    # 格式化模板
                    try:
                        formatted_prompt = template.format(**context)
                    except KeyError:
                        formatted_prompt = template
                        
                    return formatted_prompt
                    
            # 使用LLM生成主动内容
            prompt = f"作为小雪，根据当前情况({trigger_type})主动发起一个话题或表达想法。保持角色特征，语言要可爱自然。"
            
            if context:
                prompt += f"\n当前上下文: {context}"
                
            messages = [{"role": "user", "content": prompt}]
            
            if self.client:
                response = await self._call_llm(messages, is_proactive=True)
            else:
                response = self._simulate_proactive_response(trigger_type, context)
                
            return response
            
        except Exception as e:
            logger.error(f"生成主动内容失败: {e}")
            return self._get_fallback_proactive_response(trigger_type)
            
    def _build_messages(self, user_input: str, context: Dict[str, Any] = None) -> List[Dict[str, str]]:
        """构建消息列表"""
        messages = []
        
        # 系统提示
        system_prompt = self.config.get("character_prompts", {}).get("system_prompt", "")
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
            
        # 添加上下文信息
        if context:
            context_info = self._format_context(context)
            if context_info:
                messages.append({"role": "system", "content": f"当前状态: {context_info}"})
                
        # 添加对话历史
        max_history = self.config.get("conversation_settings", {}).get("max_history_length", 10)
        recent_history = self.conversation_history[-max_history:]
        
        for entry in recent_history:
            messages.append({"role": "user", "content": entry["user"]})
            messages.append({"role": "assistant", "content": entry["assistant"]})
            
        # 添加当前用户输入
        messages.append({"role": "user", "content": user_input})
        
        return messages
        
    def _format_context(self, context: Dict[str, Any]) -> str:
        """格式化上下文信息"""
        context_parts = []
        
        # 当前情感状态
        if hasattr(character, 'current_emotion'):
            context_parts.append(f"当前情感: {character.current_emotion.value}")
            
        # 沉默时长
        if 'silence_duration' in context:
            context_parts.append(f"沉默时长: {context['silence_duration']:.1f}秒")
            
        # 最近发现的文件
        if 'recent_file' in context:
            context_parts.append(f"最近发现: {context['recent_file']}")
            
        # 用户情绪
        if 'user_mood' in context:
            context_parts.append(f"用户情绪: {context['user_mood']}")
            
        return " | ".join(context_parts)
        
    async def _call_llm(self, messages: List[Dict[str, str]], is_proactive: bool = False) -> str:
        """调用LLM API"""
        try:
            # 获取生成设置
            gen_settings = self.config.get("generation_settings", {})
            
            # 调整主动对话的参数
            if is_proactive:
                gen_settings = gen_settings.copy()
                gen_settings["temperature"] = min(1.0, gen_settings.get("temperature", 0.8) + 0.1)
                gen_settings["max_tokens"] = min(gen_settings.get("max_tokens", 1000), 200)
                
            # 调用API
            response = self.client.chat.completions.create(
                model=self.config.get("model"),
                messages=messages,
                **gen_settings
            )
            
            # 提取回复内容
            content = response.choices[0].message.content
            
            logger.debug(f"LLM回复: {content[:100]}...")
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"LLM API调用失败: {e}")
            raise
            
    def _simulate_response(self, user_input: str, context: Dict[str, Any] = None) -> str:
        """模拟LLM回复"""
        # 简单的模拟回复逻辑
        current_emotion = getattr(character, 'current_emotion', EmotionalState.CURIOUS)
        
        # 根据情感状态选择回复模板
        emotion_responses = self.config.get("character_prompts", {}).get("emotion_expressions", {})
        
        if current_emotion.value in emotion_responses:
            import random
            expressions = emotion_responses[current_emotion.value]
            base_response = random.choice(expressions)
        else:
            base_response = "嗯嗯，听起来很有趣呢~"
            
        # 添加对用户输入的简单回应
        if "你好" in user_input or "hi" in user_input.lower():
            return "你好呀爸爸！" + base_response
        elif "心情" in user_input:
            return f"{base_response} 你今天心情怎么样呀？"
        else:
            return f"{base_response} 能告诉我更多吗？"
            
    def _simulate_proactive_response(self, trigger_type: str, context: Dict[str, Any] = None) -> str:
        """模拟主动回复"""
        proactive_templates = {
            "silence_detected": "爸爸？你还在吗？我有点想你了...",
            "emotional_excitement": "爸爸爸爸！我好兴奋呀~",
            "file_discovery": "诶！我发现了一个有趣的东西！",
            "seeking_attention": "爸爸~陪我聊聊天好不好？"
        }
        
        return proactive_templates.get(trigger_type, "爸爸，我们聊聊天吧~")
        
    def _get_fallback_response(self) -> str:
        """获取备用回复"""
        fallback_responses = [
            "诶？我刚才有点走神了，你能再说一遍吗？",
            "嗯嗯，我在听呢~",
            "听起来很有趣呢！能详细说说吗？",
            "我现在心情很好呢，你呢？"
        ]
        
        import random
        return random.choice(fallback_responses)
        
    def _get_fallback_proactive_response(self, trigger_type: str) -> str:
        """获取备用主动回复"""
        fallback_proactive = {
            "silence_detected": "爸爸，你在想什么呀？",
            "emotional_excitement": "我现在好开心呀~",
            "file_discovery": "我想和你分享一个发现！",
            "seeking_attention": "爸爸，陪我说说话嘛~"
        }
        
        return fallback_proactive.get(trigger_type, "爸爸，我们聊聊天吧~")
        
    def _update_conversation_history(self, user_input: str, assistant_response: str):
        """更新对话历史"""
        self.conversation_history.append({
            "user": user_input,
            "assistant": assistant_response,
            "timestamp": time.time()
        })
        
        # 限制历史长度
        max_history = self.config.get("conversation_settings", {}).get("max_history_length", 20)
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]
            
    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计信息"""
        return {
            "total_exchanges": len(self.conversation_history),
            "last_response_time": self.last_response_time,
            "client_available": self.client is not None,
            "model": self.config.get("model", "unknown")
        }
        
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        logger.info("对话历史已清空")


# 全局LLM客户端实例
llm_client = LLMClient()
